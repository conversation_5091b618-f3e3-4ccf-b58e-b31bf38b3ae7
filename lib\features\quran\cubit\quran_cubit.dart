import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/services/logger_service.dart';
import '../models/quran_models.dart';
import '../services/quran_api_service.dart';
import '../services/quran_database_service.dart';
import 'quran_state.dart';

/// Cubit for managing Quran functionality
class QuranCubit extends Cubit<QuranState> {
  QuranCubit() : super(QuranInitial());

  // Current preferences
  QuranReadingPreferences _preferences = const QuranReadingPreferences();

  // Cache
  List<SurahModel> _cachedSurahs = [];
  List<BookmarkModel> _bookmarks = [];
  List<Map<String, dynamic>> _availableTranslations = [];

  // Getters
  List<BookmarkModel> get bookmarks => _bookmarks;

  /// Initialize Quran feature quickly (for Islamic features card)
  Future<void> initializeQuranQuick() async {
    try {
      LoggerService.quran('📖 QURAN: Quick initializing Quran feature...');

      // Load preferences quickly
      _preferences = await QuranDatabaseService.getPreferences();

      // Check if we have cached Surahs
      final cachedSurahs = await QuranDatabaseService.getCachedSurahs();
      if (cachedSurahs.isNotEmpty) {
        _cachedSurahs = cachedSurahs;
        emit(
          QuranSurahsLoaded(surahs: _cachedSurahs, preferences: _preferences),
        );
        LoggerService.quran(
          '📖 QURAN: Quick initialization complete with cached data',
        );
        return;
      }

      // If no cached data, fall back to full initialization
      await initializeQuran();
    } catch (e) {
      LoggerService.error('❌ QURAN: Quick initialization failed: $e');
      // Fall back to full initialization
      await initializeQuran();
    }
  }

  /// Initialize Quran feature
  Future<void> initializeQuran() async {
    try {
      emit(const QuranLoading(progress: 0.0, message: 'Starting Quran...'));
      LoggerService.quran('📖 QURAN: Initializing Quran feature...');

      // Step 1: Initialize database (0-20%)
      emit(
        const QuranLoading(progress: 0.1, message: 'Setting up database...'),
      );
      await QuranDatabaseService.initializeDatabase();
      emit(const QuranLoading(progress: 0.2, message: 'Database ready'));

      // Step 2: Load preferences (20-30%)
      emit(
        const QuranLoading(progress: 0.25, message: 'Loading preferences...'),
      );
      _preferences = await QuranDatabaseService.getPreferences();
      emit(const QuranLoading(progress: 0.3, message: 'Preferences loaded'));

      // Step 3: Check API availability (30-40%)
      emit(
        const QuranLoading(progress: 0.35, message: 'Checking connection...'),
      );
      final isApiAvailable = await QuranApiService.checkApiAvailability();
      if (!isApiAvailable) {
        emit(
          const QuranNetworkError(
            'Quran service is currently unavailable. Please check your internet connection and try again.',
          ),
        );
        return;
      }
      emit(const QuranLoading(progress: 0.4, message: 'Connection verified'));

      // Step 4: Load Surahs (40-70%)
      emit(const QuranLoading(progress: 0.5, message: 'Loading Surahs...'));
      await _loadSurahs();
      emit(const QuranLoading(progress: 0.7, message: 'Surahs loaded'));

      // Step 5: Load bookmarks (70-80%)
      emit(const QuranLoading(progress: 0.75, message: 'Loading bookmarks...'));
      _bookmarks = await QuranDatabaseService.getBookmarks();
      emit(const QuranLoading(progress: 0.8, message: 'Bookmarks loaded'));

      // Step 6: Load translations (80-100%)
      emit(
        const QuranLoading(progress: 0.9, message: 'Loading translations...'),
      );
      await _loadAvailableTranslations();
      emit(const QuranLoading(progress: 1.0, message: 'Complete'));

      // Emit final state
      emit(QuranSurahsLoaded(surahs: _cachedSurahs, preferences: _preferences));

      LoggerService.quran(
        '📖 QURAN: Initialization complete with ${_cachedSurahs.length} Surahs',
      );
    } catch (e) {
      LoggerService.error('❌ QURAN: Initialization error: $e');
      emit(QuranError('Failed to initialize Quran: ${e.toString()}'));
    }
  }

  /// Load all Surahs
  Future<void> _loadSurahs() async {
    try {
      // Try to load from cache first
      _cachedSurahs = await QuranDatabaseService.getCachedSurahs();

      if (_cachedSurahs.isEmpty) {
        // Load from API if cache is empty
        _cachedSurahs = await QuranApiService.getAllSurahs();

        // Cache the Surahs
        await QuranDatabaseService.cacheSurahs(_cachedSurahs);
      }

      LoggerService.quran('📖 QURAN: Loaded ${_cachedSurahs.length} Surahs');
    } catch (e) {
      LoggerService.error('❌ QURAN: Error loading Surahs: $e');
      throw Exception('Unable to load Surahs: ${e.toString()}');
    }
  }

  /// Load available translations
  Future<void> _loadAvailableTranslations() async {
    try {
      _availableTranslations = await QuranApiService.getAvailableTranslations();
      LoggerService.quran(
        '📖 QURAN: Loaded ${_availableTranslations.length} translations',
      );
    } catch (e) {
      LoggerService.error('❌ QURAN: Error loading translations: $e');
      // Use only the best Sunni translation if API fails
      _availableTranslations = [
        {
          'identifier': 'en.sahih',
          'language': 'en',
          'name': 'Sahih International',
          'englishName': 'Sahih International - Best Sunni Translation',
          'format': 'text',
          'type': 'translation',
        },
      ];
    }
  }

  /// Open specific Surah for reading with proper loading states
  Future<void> openSurah(int surahNumber) async {
    try {
      // Show loading state immediately
      emit(const QuranLoading(progress: 0.3, message: 'Loading Surah...'));

      // Check if we have cached Surah with translation
      final cachedSurah =
          await QuranDatabaseService.getCachedSurahWithTranslation(surahNumber);

      if (cachedSurah != null && cachedSurah.ayahs.isNotEmpty) {
        // Instant loading from cache
        emit(
          QuranSurahReading(
            surah: cachedSurah,
            preferences: _preferences,
            bookmarks: _bookmarks,
          ),
        );
        LoggerService.quran(
          '📖 QURAN: Opened cached Surah ${cachedSurah.englishName}',
        );
        return;
      }

      // Update loading progress
      emit(
        const QuranLoading(progress: 0.6, message: 'Fetching Surah content...'),
      );

      // If not cached or empty, load from API
      final surah = await QuranApiService.getSurahWithTranslation(
        surahNumber,
        'en.sahih', // Sahih International - Best Sunni English translation
      );

      // Validate that we got content
      if (surah.ayahs.isEmpty) {
        throw Exception('Surah content is empty. Please try again.');
      }

      // Update loading progress
      emit(const QuranLoading(progress: 0.9, message: 'Preparing content...'));

      // Cache the Surah with translation for next time
      await QuranDatabaseService.cacheSurahWithTranslation(surah);

      // Emit reading state
      emit(
        QuranSurahReading(
          surah: surah,
          preferences: _preferences,
          bookmarks: _bookmarks,
        ),
      );

      LoggerService.quran(
        '📖 QURAN: Opened Surah ${surah.englishName} with ${surah.ayahs.length} ayahs',
      );
    } catch (e) {
      LoggerService.error('❌ QURAN: Error opening Surah $surahNumber: $e');
      emit(
        QuranError(
          'Unable to load Surah. Please check your connection and try again.\n\nError: ${e.toString()}',
        ),
      );
    }
  }

  /// Search in Quran
  Future<void> searchQuran(String query) async {
    if (query.trim().isEmpty) {
      // Return to Surahs list
      emit(QuranSurahsLoaded(surahs: _cachedSurahs, preferences: _preferences));
      return;
    }

    try {
      emit(const QuranLoading(progress: 0.5, message: 'Searching Quran...'));

      final results = await QuranApiService.searchQuran(query, 'en');

      emit(
        QuranSearchResults(
          results: results,
          query: query,
          preferences: _preferences,
        ),
      );

      LoggerService.quran(
        '📖 QURAN: Found ${results.length} results for "$query"',
      );
    } catch (e) {
      LoggerService.error('❌ QURAN: Search error: $e');
      emit(QuranError('Unable to search Quran: ${e.toString()}'));
    }
  }

  /// Add bookmark
  Future<void> addBookmark(
    int surahNumber,
    int ayahNumber,
    String surahName, {
    String note = '',
  }) async {
    try {
      final bookmark = BookmarkModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        surahNumber: surahNumber,
        ayahNumber: ayahNumber,
        surahName: surahName,
        note: note,
        createdAt: DateTime.now(),
      );

      await QuranDatabaseService.addBookmark(bookmark);
      _bookmarks = await QuranDatabaseService.getBookmarks();

      // Update current state with new bookmarks
      final currentState = state;
      if (currentState is QuranSurahReading) {
        emit(currentState.copyWith(bookmarks: _bookmarks));
      }

      LoggerService.quran(
        '📖 QURAN: Added bookmark for $surahName $ayahNumber',
      );
    } catch (e) {
      LoggerService.error('❌ QURAN: Error adding bookmark: $e');
    }
  }

  /// Remove bookmark - SAFE VERSION
  Future<void> removeBookmark(String bookmarkId) async {
    try {
      // Check if bookmark exists first
      final bookmarkExists = _bookmarks.any((b) => b.id == bookmarkId);
      if (!bookmarkExists) {
        LoggerService.quran('📖 QURAN: Bookmark $bookmarkId already removed');
        return;
      }

      LoggerService.quran('📖 QURAN: Removing bookmark $bookmarkId');

      // Remove from local list first
      _bookmarks.removeWhere((b) => b.id == bookmarkId);

      // Remove from database
      await QuranDatabaseService.removeBookmark(bookmarkId);

      // Update UI immediately
      emit(QuranBookmarks(bookmarks: _bookmarks, preferences: _preferences));

      LoggerService.quran('📖 QURAN: Successfully removed bookmark');
    } catch (e) {
      LoggerService.error('❌ QURAN: Error removing bookmark $bookmarkId: $e');
    }
  }

  /// Clear all bookmarks - SAFE VERSION
  Future<void> clearAllBookmarks() async {
    try {
      LoggerService.quran('📖 QURAN: Clearing all bookmarks');

      // Get all bookmark IDs before clearing
      final bookmarkIds = _bookmarks.map((b) => b.id).toList();

      // Clear local list first
      _bookmarks.clear();

      // Remove from database
      for (final id in bookmarkIds) {
        await QuranDatabaseService.removeBookmark(id);
      }

      // Update UI
      emit(QuranBookmarks(bookmarks: _bookmarks, preferences: _preferences));

      LoggerService.quran('📖 QURAN: Cleared ${bookmarkIds.length} bookmarks');
    } catch (e) {
      LoggerService.error('❌ QURAN: Error clearing bookmarks: $e');
    }
  }

  /// Show bookmarks
  void showBookmarks() {
    emit(QuranBookmarks(bookmarks: _bookmarks, preferences: _preferences));
  }

  /// Update preferences
  Future<void> updatePreferences(QuranReadingPreferences newPreferences) async {
    try {
      _preferences = newPreferences;
      await QuranDatabaseService.savePreferences(_preferences);

      // Update current state with new preferences
      final currentState = state;
      if (currentState is QuranSurahsLoaded) {
        emit(currentState.copyWith(preferences: _preferences));
      } else if (currentState is QuranSurahReading) {
        emit(currentState.copyWith(preferences: _preferences));
      } else if (currentState is QuranSettings) {
        emit(currentState.copyWith(preferences: _preferences));
      }

      LoggerService.quran('📖 QURAN: Updated preferences');
    } catch (e) {
      LoggerService.error('❌ QURAN: Error updating preferences: $e');
    }
  }

  /// Show settings
  void showSettings() {
    emit(
      QuranSettings(
        preferences: _preferences,
        availableTranslations: _availableTranslations,
      ),
    );
  }

  /// Toggle bookmark for a Surah (optimized - no state emission)
  Future<void> toggleSurahBookmark(SurahModel surah) async {
    try {
      final existingBookmark = _bookmarks.firstWhere(
        (b) => b.surahNumber == surah.number,
        orElse:
            () => BookmarkModel(
              id: '',
              surahNumber: -1,
              ayahNumber: 0,
              surahName: '',
              note: '',
              createdAt: DateTime.now(),
            ),
      );

      if (existingBookmark.surahNumber == surah.number) {
        // Remove bookmark
        await QuranDatabaseService.removeBookmark(existingBookmark.id);
        _bookmarks.removeWhere((b) => b.id == existingBookmark.id);
        LoggerService.quran(
          '📖 QURAN: Removed bookmark for ${surah.englishName}',
        );
      } else {
        // Add bookmark
        final bookmark = BookmarkModel(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          surahNumber: surah.number,
          ayahNumber: 1, // First ayah
          surahName: surah.englishName,
          note: 'Bookmarked Surah',
          createdAt: DateTime.now(),
        );

        await QuranDatabaseService.addBookmark(bookmark);
        _bookmarks.add(bookmark);
        LoggerService.quran(
          '📖 QURAN: Added bookmark for ${surah.englishName}',
        );
      }

      // Don't emit state change to avoid rebuilding entire UI
      // Individual bookmark buttons will update themselves
    } catch (e) {
      LoggerService.error('❌ QURAN: Error toggling bookmark: $e');
    }
  }

  /// Go back to Surahs list
  void backToSurahs() {
    emit(QuranSurahsLoaded(surahs: _cachedSurahs, preferences: _preferences));
  }

  /// Refresh Quran data
  Future<void> refreshQuran() async {
    // Clear cache and reload
    await QuranDatabaseService.clearCache();
    await initializeQuran();
  }

  /// Set current Ayah (for reading progress)
  void setCurrentAyah(int ayahIndex) {
    final currentState = state;
    if (currentState is QuranSurahReading) {
      emit(currentState.copyWith(currentAyahIndex: ayahIndex));
    }
  }

  /// Open Juz (Para)
  Future<void> openJuz(int juzNumber) async {
    try {
      emit(const QuranLoading(progress: 0.5, message: 'Loading Juz...'));

      final juz = await QuranApiService.getJuz(juzNumber);

      emit(QuranJuzLoaded(juz: juz, preferences: _preferences));

      LoggerService.quran('📖 QURAN: Opened Juz $juzNumber');
    } catch (e) {
      LoggerService.error('❌ QURAN: Error opening Juz: $e');
      emit(QuranError('Unable to open Juz: ${e.toString()}'));
    }
  }
}
