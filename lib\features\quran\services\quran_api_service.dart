import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../core/services/logger_service.dart';
import '../models/quran_models.dart';

/// Service for fetching Quran data from Al-Quran Cloud API (FREE)
class QuranApiService {
  static const String _baseUrl = 'https://api.alquran.cloud/v1';
  static const int _timeoutSeconds = 30;

  /// Get all Surahs list
  static Future<List<SurahModel>> getAllSurahs() async {
    try {
      LoggerService.quran('📖 QURAN: Fetching all Surahs...');

      final response = await http
          .get(Uri.parse('$_baseUrl/surah'))
          .timeout(Duration(seconds: _timeoutSeconds));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final surahs =
            (data['data'] as List<dynamic>)
                .map((surah) => SurahModel.fromJson(surah))
                .toList();

        LoggerService.quran('📖 QURAN: Retrieved ${surahs.length} Surahs');
        return surahs;
      } else {
        throw Exception('Failed to fetch Surahs: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.error('❌ QURAN: Error fetching Surahs: $e');
      throw Exception('Unable to fetch Quran Surahs: ${e.toString()}');
    }
  }

  /// Get specific Surah with Arabic text
  static Future<SurahModel> getSurah(int surahNumber) async {
    try {
      LoggerService.quran('📖 QURAN: Fetching Surah $surahNumber...');

      final response = await http
          .get(Uri.parse('$_baseUrl/surah/$surahNumber'))
          .timeout(Duration(seconds: _timeoutSeconds));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final surah = SurahModel.fromJson(data['data']);

        LoggerService.quran(
          '📖 QURAN: Retrieved Surah ${surah.englishName} with ${surah.numberOfAyahs} Ayahs',
        );
        return surah;
      } else {
        throw Exception('Failed to fetch Surah: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.error('❌ QURAN: Error fetching Surah $surahNumber: $e');
      throw Exception('Unable to fetch Surah: ${e.toString()}');
    }
  }

  /// Get Surah with translation
  static Future<SurahModel> getSurahWithTranslation(
    int surahNumber,
    String translationId,
  ) async {
    try {
      LoggerService.quran(
        '📖 QURAN: Fetching Surah $surahNumber with translation $translationId...',
      );

      // Get Arabic text
      final arabicResponse = await http
          .get(Uri.parse('$_baseUrl/surah/$surahNumber'))
          .timeout(Duration(seconds: _timeoutSeconds));

      // Get translation
      final translationResponse = await http
          .get(Uri.parse('$_baseUrl/surah/$surahNumber/$translationId'))
          .timeout(Duration(seconds: _timeoutSeconds));

      if (arabicResponse.statusCode == 200 &&
          translationResponse.statusCode == 200) {
        final arabicData = json.decode(arabicResponse.body);
        final translationData = json.decode(translationResponse.body);

        final surah = SurahModel.fromJson(arabicData['data']);
        final translationSurah = SurahModel.fromJson(translationData['data']);

        // Combine Arabic and translation
        final combinedAyahs = <AyahModel>[];
        for (int i = 0; i < surah.ayahs.length; i++) {
          final arabicAyah = surah.ayahs[i];
          final translationText =
              i < translationSurah.ayahs.length
                  ? translationSurah.ayahs[i].text
                  : '';

          combinedAyahs.add(
            AyahModel(
              number: arabicAyah.number,
              text: arabicAyah.text,
              numberInSurah: arabicAyah.numberInSurah,
              juz: arabicAyah.juz,
              manzil: arabicAyah.manzil,
              page: arabicAyah.page,
              ruku: arabicAyah.ruku,
              hizbQuarter: arabicAyah.hizbQuarter,
              sajda: arabicAyah.sajda,
              translations: [
                TranslationModel(
                  id: 1,
                  language: 'en',
                  text: translationText,
                  resourceName: translationId,
                ),
              ],
            ),
          );
        }

        final result = surah.copyWith(ayahs: combinedAyahs);
        LoggerService.quran(
          '📖 QURAN: Retrieved Surah ${result.englishName} with translation',
        );
        return result;
      } else {
        throw Exception('Failed to fetch Surah with translation');
      }
    } catch (e) {
      LoggerService.error('❌ QURAN: Error fetching Surah with translation: $e');
      throw Exception(
        'Unable to fetch Surah with translation: ${e.toString()}',
      );
    }
  }

  /// Get specific Ayah
  static Future<AyahModel> getAyah(int surahNumber, int ayahNumber) async {
    try {
      LoggerService.quran(
        '📖 QURAN: Fetching Ayah $surahNumber:$ayahNumber...',
      );

      final response = await http
          .get(Uri.parse('$_baseUrl/ayah/$surahNumber:$ayahNumber'))
          .timeout(Duration(seconds: _timeoutSeconds));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final ayah = AyahModel.fromJson(data['data']);

        LoggerService.quran(
          '📖 QURAN: Retrieved Ayah $surahNumber:$ayahNumber',
        );
        return ayah;
      } else {
        throw Exception('Failed to fetch Ayah: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.error('❌ QURAN: Error fetching Ayah: $e');
      throw Exception('Unable to fetch Ayah: ${e.toString()}');
    }
  }

  /// Search in Quran
  static Future<List<AyahModel>> searchQuran(
    String query,
    String language,
  ) async {
    try {
      LoggerService.quran('📖 QURAN: Searching for "$query" in $language...');

      final response = await http
          .get(Uri.parse('$_baseUrl/search/$query/all/$language'))
          .timeout(Duration(seconds: _timeoutSeconds));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final matches =
            (data['data']['matches'] as List<dynamic>)
                .map((match) => AyahModel.fromJson(match))
                .toList();

        LoggerService.quran(
          '📖 QURAN: Found ${matches.length} matches for "$query"',
        );
        return matches;
      } else {
        throw Exception('Failed to search Quran: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.error('❌ QURAN: Error searching Quran: $e');
      throw Exception('Unable to search Quran: ${e.toString()}');
    }
  }

  /// Get available translations
  static Future<List<Map<String, dynamic>>> getAvailableTranslations() async {
    try {
      LoggerService.quran('📖 QURAN: Fetching available translations...');

      final response = await http
          .get(Uri.parse('$_baseUrl/edition/type/translation'))
          .timeout(Duration(seconds: _timeoutSeconds));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final translations =
            (data['data'] as List<dynamic>).cast<Map<String, dynamic>>();

        LoggerService.quran(
          '📖 QURAN: Retrieved ${translations.length} translations',
        );
        return translations;
      } else {
        throw Exception('Failed to fetch translations: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.error('❌ QURAN: Error fetching translations: $e');
      throw Exception('Unable to fetch translations: ${e.toString()}');
    }
  }

  /// Get Juz (Para) information
  static Future<JuzModel> getJuz(int juzNumber) async {
    try {
      LoggerService.quran('📖 QURAN: Fetching Juz $juzNumber...');

      final response = await http
          .get(Uri.parse('$_baseUrl/juz/$juzNumber'))
          .timeout(Duration(seconds: _timeoutSeconds));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final surahs =
            (data['data']['surahs'] as Map<String, dynamic>).values
                .map((surah) => SurahModel.fromJson(surah))
                .toList();

        final juz = JuzModel(number: juzNumber, surahs: surahs);
        LoggerService.quran(
          '📖 QURAN: Retrieved Juz $juzNumber with ${surahs.length} Surahs',
        );
        return juz;
      } else {
        throw Exception('Failed to fetch Juz: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.error('❌ QURAN: Error fetching Juz: $e');
      throw Exception('Unable to fetch Juz: ${e.toString()}');
    }
  }

  /// Check API availability
  static Future<bool> checkApiAvailability() async {
    try {
      final response = await http
          .get(Uri.parse('$_baseUrl/surah/1'))
          .timeout(Duration(seconds: 10));

      return response.statusCode == 200;
    } catch (e) {
      LoggerService.error('❌ QURAN: API unavailable: $e');
      return false;
    }
  }
}
