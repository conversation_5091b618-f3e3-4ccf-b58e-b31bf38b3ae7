{"buildFiles": ["C:\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutter\\islam\\muslim\\noor_islamic_app\\android\\app\\.cxx\\Debug\\4f3y3p6y\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutter\\islam\\muslim\\noor_islamic_app\\android\\app\\.cxx\\Debug\\4f3y3p6y\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}