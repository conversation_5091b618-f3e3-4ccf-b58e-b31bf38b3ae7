import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';
import '../../../core/services/logger_service.dart';
import '../models/mosque_model.dart';

/// Service for fetching mosque data from OpenStreetMap using Overpass API
class OverpassApiService {
  static const String _baseUrl = 'https://overpass-api.de/api/interpreter';
  static const int _timeoutSeconds = 30;

  /// Fetch mosques near a location
  static Future<List<MosqueModel>> fetchMosquesNearLocation({
    required double latitude,
    required double longitude,
    required double radiusKm,
  }) async {
    try {
      LoggerService.mosque('🕌 MOSQUE: Fetching mosques near ($latitude, $longitude) within ${radiusKm}km');

      final query = _buildOverpassQuery(latitude, longitude, radiusKm);
      
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'data=$query',
      ).timeout(Duration(seconds: _timeoutSeconds));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final elements = data['elements'] as List<dynamic>? ?? [];
        
        final mosques = elements
            .where((element) => element['type'] == 'node' || element['type'] == 'way')
            .map((element) => MosqueModel.fromOSM(element))
            .where((mosque) => mosque.name.isNotEmpty)
            .toList();

        // Calculate distances and sort by proximity
        final mosquesWithDistance = mosques.map((mosque) {
          final distance = Geolocator.distanceBetween(
            latitude,
            longitude,
            mosque.latitude,
            mosque.longitude,
          ) / 1000; // Convert to kilometers

          return mosque.copyWith(distanceFromUser: distance);
        }).toList();

        mosquesWithDistance.sort((a, b) => 
          (a.distanceFromUser ?? 0).compareTo(b.distanceFromUser ?? 0));

        LoggerService.mosque('🕌 MOSQUE: Found ${mosquesWithDistance.length} mosques');
        return mosquesWithDistance;
      } else {
        throw Exception('Failed to fetch mosque data: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.error('❌ MOSQUE: Error fetching mosques: $e');
      throw Exception('Unable to fetch mosque data: ${e.toString()}');
    }
  }

  /// Search mosques by name or location
  static Future<List<MosqueModel>> searchMosques({
    required String query,
    required double latitude,
    required double longitude,
    double radiusKm = 50.0,
  }) async {
    try {
      LoggerService.mosque('🕌 MOSQUE: Searching for "$query" near ($latitude, $longitude)');

      final overpassQuery = _buildSearchQuery(query, latitude, longitude, radiusKm);
      
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'data=$overpassQuery',
      ).timeout(Duration(seconds: _timeoutSeconds));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final elements = data['elements'] as List<dynamic>? ?? [];
        
        final mosques = elements
            .where((element) => element['type'] == 'node' || element['type'] == 'way')
            .map((element) => MosqueModel.fromOSM(element))
            .where((mosque) => 
              mosque.name.toLowerCase().contains(query.toLowerCase()) ||
              mosque.address.toLowerCase().contains(query.toLowerCase()))
            .toList();

        // Calculate distances
        final mosquesWithDistance = mosques.map((mosque) {
          final distance = Geolocator.distanceBetween(
            latitude,
            longitude,
            mosque.latitude,
            mosque.longitude,
          ) / 1000;

          return mosque.copyWith(distanceFromUser: distance);
        }).toList();

        mosquesWithDistance.sort((a, b) => 
          (a.distanceFromUser ?? 0).compareTo(b.distanceFromUser ?? 0));

        LoggerService.mosque('🕌 MOSQUE: Found ${mosquesWithDistance.length} mosques matching "$query"');
        return mosquesWithDistance;
      } else {
        throw Exception('Failed to search mosques: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.error('❌ MOSQUE: Error searching mosques: $e');
      throw Exception('Unable to search mosques: ${e.toString()}');
    }
  }

  /// Build Overpass query for nearby mosques
  static String _buildOverpassQuery(double lat, double lon, double radiusKm) {
    final radiusMeters = (radiusKm * 1000).toInt();
    
    return '''
[out:json][timeout:25];
(
  node["amenity"="place_of_worship"]["religion"="muslim"](around:$radiusMeters,$lat,$lon);
  way["amenity"="place_of_worship"]["religion"="muslim"](around:$radiusMeters,$lat,$lon);
  relation["amenity"="place_of_worship"]["religion"="muslim"](around:$radiusMeters,$lat,$lon);
);
out center meta;
''';
  }

  /// Build Overpass query for mosque search
  static String _buildSearchQuery(String query, double lat, double lon, double radiusKm) {
    final radiusMeters = (radiusKm * 1000).toInt();
    
    return '''
[out:json][timeout:25];
(
  node["amenity"="place_of_worship"]["religion"="muslim"]["name"~"$query",i](around:$radiusMeters,$lat,$lon);
  way["amenity"="place_of_worship"]["religion"="muslim"]["name"~"$query",i](around:$radiusMeters,$lat,$lon);
  relation["amenity"="place_of_worship"]["religion"="muslim"]["name"~"$query",i](around:$radiusMeters,$lat,$lon);
);
out center meta;
''';
  }

  /// Get mosque details by ID
  static Future<MosqueModel?> getMosqueDetails(String mosqueId) async {
    try {
      LoggerService.mosque('🕌 MOSQUE: Fetching details for mosque ID: $mosqueId');

      final query = '''
[out:json][timeout:25];
(
  node($mosqueId);
  way($mosqueId);
  relation($mosqueId);
);
out center meta;
''';

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'data=$query',
      ).timeout(Duration(seconds: _timeoutSeconds));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final elements = data['elements'] as List<dynamic>? ?? [];
        
        if (elements.isNotEmpty) {
          final mosque = MosqueModel.fromOSM(elements.first);
          LoggerService.mosque('🕌 MOSQUE: Retrieved details for ${mosque.name}');
          return mosque;
        }
      }
      
      return null;
    } catch (e) {
      LoggerService.error('❌ MOSQUE: Error fetching mosque details: $e');
      return null;
    }
  }

  /// Check if Overpass API is available
  static Future<bool> checkApiAvailability() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl?data=[out:json];out;'),
      ).timeout(Duration(seconds: 10));
      
      return response.statusCode == 200;
    } catch (e) {
      LoggerService.error('❌ MOSQUE: Overpass API unavailable: $e');
      return false;
    }
  }
}
