import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppTextStyles {
  // Display Text Styles
  static TextStyle displayLarge = GoogleFonts.poppins(
    fontSize: 57.sp,
    fontWeight: FontWeight.w400,
    letterSpacing: -0.25,
  );
  
  static TextStyle displayMedium = GoogleFonts.poppins(
    fontSize: 45.sp,
    fontWeight: FontWeight.w400,
  );
  
  static TextStyle displaySmall = GoogleFonts.poppins(
    fontSize: 36.sp,
    fontWeight: FontWeight.w400,
  );
  
  // Headline Text Styles
  static TextStyle headlineLarge = GoogleFonts.poppins(
    fontSize: 32.sp,
    fontWeight: FontWeight.w600,
  );
  
  static TextStyle headlineMedium = GoogleFonts.poppins(
    fontSize: 28.sp,
    fontWeight: FontWeight.w600,
  );
  
  static TextStyle headlineSmall = GoogleFonts.poppins(
    fontSize: 24.sp,
    fontWeight: FontWeight.w600,
  );
  
  // Title Text Styles
  static TextStyle titleLarge = GoogleFonts.inter(
    fontSize: 22.sp,
    fontWeight: FontWeight.w600,
  );
  
  static TextStyle titleMedium = GoogleFonts.inter(
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.15,
  );
  
  static TextStyle titleSmall = GoogleFonts.inter(
    fontSize: 14.sp,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.1,
  );
  
  // Body Text Styles
  static TextStyle bodyLarge = GoogleFonts.inter(
    fontSize: 16.sp,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
  );
  
  static TextStyle bodyMedium = GoogleFonts.inter(
    fontSize: 14.sp,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
  );
  
  static TextStyle bodySmall = GoogleFonts.inter(
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
  );
  
  // Label Text Styles
  static TextStyle labelLarge = GoogleFonts.inter(
    fontSize: 14.sp,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
  );
  
  static TextStyle labelMedium = GoogleFonts.inter(
    fontSize: 12.sp,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
  );
  
  static TextStyle labelSmall = GoogleFonts.inter(
    fontSize: 11.sp,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
  );
  
  // Arabic Text Styles (for Quran, Duas, etc.)
  static TextStyle arabicLarge = GoogleFonts.amiri(
    fontSize: 24.sp,
    fontWeight: FontWeight.w400,
    height: 2.0,
  );
  
  static TextStyle arabicMedium = GoogleFonts.amiri(
    fontSize: 20.sp,
    fontWeight: FontWeight.w400,
    height: 1.8,
  );
  
  static TextStyle arabicSmall = GoogleFonts.amiri(
    fontSize: 16.sp,
    fontWeight: FontWeight.w400,
    height: 1.6,
  );
  
  // Special Text Styles
  static TextStyle prayerTime = GoogleFonts.poppins(
    fontSize: 32.sp,
    fontWeight: FontWeight.w700,
    letterSpacing: -0.5,
  );
  
  static TextStyle prayerName = GoogleFonts.poppins(
    fontSize: 18.sp,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.5,
  );
  
  static TextStyle cardTitle = GoogleFonts.poppins(
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
  );
  
  static TextStyle cardSubtitle = GoogleFonts.inter(
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
  );
}
