import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/quran_models.dart';

class QuranSettingsView extends StatefulWidget {
  final QuranReadingPreferences preferences;
  final List<Map<String, dynamic>> availableTranslations;
  final Function(QuranReadingPreferences) onPreferencesChanged;

  const QuranSettingsView({
    super.key,
    required this.preferences,
    required this.availableTranslations,
    required this.onPreferencesChanged,
  });

  @override
  State<QuranSettingsView> createState() => _QuranSettingsViewState();
}

class _QuranSettingsViewState extends State<QuranSettingsView> {
  late QuranReadingPreferences _currentPreferences;

  @override
  void initState() {
    super.initState();
    _currentPreferences = widget.preferences;
  }

  void _updatePreferences(QuranReadingPreferences newPreferences) {
    setState(() {
      _currentPreferences = newPreferences;
    });
    widget.onPreferencesChanged(newPreferences);
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display Settings
          _buildSectionTitle('Display Settings'),
          SizedBox(height: 16.h),
          _buildDisplaySettings(),

          SizedBox(height: 24.h),

          // Font Settings
          _buildSectionTitle('Font Settings'),
          SizedBox(height: 16.h),
          _buildFontSettings(),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: AppTextStyles.titleLarge.copyWith(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? Colors.white
                : Colors.black87,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildDisplaySettings() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? Colors.white.withValues(alpha: 0.1)
                : Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withValues(alpha: 0.2)
                  : Colors.black.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        children: [
          // Show Arabic
          SwitchListTile(
            title: Text(
              'Show Arabic Text',
              style: AppTextStyles.bodyMedium.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
              ),
            ),
            subtitle: Text(
              'Display original Arabic verses',
              style: AppTextStyles.bodySmall.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.7)
                        : Colors.black.withValues(alpha: 0.6),
              ),
            ),
            value: _currentPreferences.showArabic,
            onChanged: (value) {
              _updatePreferences(
                _currentPreferences.copyWith(showArabic: value),
              );
            },
            activeColor: AppColors.primaryGreen,
            contentPadding: EdgeInsets.zero,
          ),

          // Show Translation
          SwitchListTile(
            title: Text(
              'Show Translation',
              style: AppTextStyles.bodyMedium.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
              ),
            ),
            subtitle: Text(
              'Display English translation',
              style: AppTextStyles.bodySmall.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.7)
                        : Colors.black.withValues(alpha: 0.6),
              ),
            ),
            value: _currentPreferences.showTranslation,
            onChanged: (value) {
              _updatePreferences(
                _currentPreferences.copyWith(showTranslation: value),
              );
            },
            activeColor: AppColors.primaryGreen,
            contentPadding: EdgeInsets.zero,
          ),
        ],
      ),
    );
  }

  Widget _buildFontSettings() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? Colors.white.withValues(alpha: 0.1)
                : Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withValues(alpha: 0.2)
                  : Colors.black.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Arabic Font Size
          Text(
            'Arabic Font Size: ${_currentPreferences.arabicFontSize.toInt()}',
            style: AppTextStyles.bodyMedium.copyWith(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black87,
            ),
          ),

          Slider(
            value: _currentPreferences.arabicFontSize,
            min: 16.0,
            max: 36.0,
            divisions: 20,
            onChanged: (value) {
              _updatePreferences(
                _currentPreferences.copyWith(arabicFontSize: value),
              );
            },
            activeColor: AppColors.primaryGreen,
          ),

          SizedBox(height: 16.h),

          // Translation Font Size
          Text(
            'Translation Font Size: ${_currentPreferences.translationFontSize.toInt()}',
            style: AppTextStyles.bodyMedium.copyWith(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black87,
            ),
          ),

          Slider(
            value: _currentPreferences.translationFontSize,
            min: 12.0,
            max: 24.0,
            divisions: 12,
            onChanged: (value) {
              _updatePreferences(
                _currentPreferences.copyWith(translationFontSize: value),
              );
            },
            activeColor: AppColors.primaryGreen,
          ),

          SizedBox(height: 16.h),

          // Line Spacing
          Text(
            'Line Spacing: ${_currentPreferences.lineSpacing.toStringAsFixed(1)}',
            style: AppTextStyles.bodyMedium.copyWith(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black87,
            ),
          ),

          Slider(
            value: _currentPreferences.lineSpacing,
            min: 1.0,
            max: 2.5,
            divisions: 15,
            onChanged: (value) {
              _updatePreferences(
                _currentPreferences.copyWith(lineSpacing: value),
              );
            },
            activeColor: AppColors.primaryGreen,
          ),
        ],
      ),
    );
  }
}
