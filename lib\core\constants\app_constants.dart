class AppConstants {
  // App Info
  static const String appName = 'Noor Islamic App';
  static const String appVersion = '1.0.0';

  // API URLs
  static const String prayerTimesApiUrl = 'https://api.aladhan.com/v1';
  static const String mosqueFinderApiUrl = 'https://api.foursquare.com/v3';

  // Storage Keys
  static const String themeKey = 'theme_mode';
  static const String locationKey = 'user_location';
  static const String prayerSettingsKey = 'prayer_settings';
  static const String lastPrayerTimesKey = 'last_prayer_times';

  // Prayer Names
  static const List<String> prayerNames = [
    'Fajr',
    '<PERSON>hu<PERSON>',
    'Asr',
    'Maghrib',
    '<PERSON><PERSON>',
  ];

  // Islamic Features
  static const List<String> islamicFeatures = [
    'Prayer Times',
    'Qibla Finder',
    'Mosque Finder',
    'Full Quran',
    'Tafsir Quran',
    'Hadith',
    'Duas',
    '99 Names',
  ];

  // Default Values
  static const double defaultLatitude = 21.3891; // Mecca
  static const double defaultLongitude = 39.8579; // Mecca

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 300);
  static const Duration mediumAnimation = Duration(milliseconds: 500);
  static const Duration longAnimation = Duration(milliseconds: 800);
}
