import 'package:equatable/equatable.dart';

/// Dua Category Model
class DuaCategoryModel extends Equatable {
  final String id;
  final String name;
  final String description;
  final String iconPath;
  final String colorHex;
  final int duasCount;
  final int orderIndex;
  final bool isActive;

  const DuaCategoryModel({
    required this.id,
    required this.name,
    required this.description,
    required this.iconPath,
    required this.colorHex,
    required this.duasCount,
    required this.orderIndex,
    this.isActive = true,
  });

  factory DuaCategoryModel.fromJson(Map<String, dynamic> json) {
    return DuaCategoryModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      iconPath: json['icon_path'] ?? '',
      colorHex: json['color_hex'] ?? '#4CAF50',
      duasCount: json['duas_count'] ?? 0,
      orderIndex: json['order_index'] ?? 0,
      isActive: json['is_active'] == 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon_path': iconPath,
      'color_hex': colorHex,
      'duas_count': duasCount,
      'order_index': orderIndex,
      'is_active': isActive ? 1 : 0,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        iconPath,
        colorHex,
        duasCount,
        orderIndex,
        isActive,
      ];
}

/// Individual Dua Model
class DuaModel extends Equatable {
  final String id;
  final String categoryId;
  final String title;
  final String arabicText;
  final String transliteration;
  final String englishTranslation;
  final String simpleExplanation;
  final List<String> benefits;
  final List<String> whenToRecite;
  final List<String> realLifeExamples;
  final String hadithReference;
  final String audioUrl;
  final int orderIndex;
  final bool isFavorite;
  final DateTime createdAt;

  const DuaModel({
    required this.id,
    required this.categoryId,
    required this.title,
    required this.arabicText,
    required this.transliteration,
    required this.englishTranslation,
    required this.simpleExplanation,
    required this.benefits,
    required this.whenToRecite,
    required this.realLifeExamples,
    required this.hadithReference,
    this.audioUrl = '',
    required this.orderIndex,
    this.isFavorite = false,
    required this.createdAt,
  });

  factory DuaModel.fromJson(Map<String, dynamic> json) {
    return DuaModel(
      id: json['id'] ?? '',
      categoryId: json['category_id'] ?? '',
      title: json['title'] ?? '',
      arabicText: json['arabic_text'] ?? '',
      transliteration: json['transliteration'] ?? '',
      englishTranslation: json['english_translation'] ?? '',
      simpleExplanation: json['simple_explanation'] ?? '',
      benefits: _parseStringList(json['benefits']),
      whenToRecite: _parseStringList(json['when_to_recite']),
      realLifeExamples: _parseStringList(json['real_life_examples']),
      hadithReference: json['hadith_reference'] ?? '',
      audioUrl: json['audio_url'] ?? '',
      orderIndex: json['order_index'] ?? 0,
      isFavorite: json['is_favorite'] == 1,
      createdAt: DateTime.parse(
        json['created_at'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category_id': categoryId,
      'title': title,
      'arabic_text': arabicText,
      'transliteration': transliteration,
      'english_translation': englishTranslation,
      'simple_explanation': simpleExplanation,
      'benefits': _stringListToJson(benefits),
      'when_to_recite': _stringListToJson(whenToRecite),
      'real_life_examples': _stringListToJson(realLifeExamples),
      'hadith_reference': hadithReference,
      'audio_url': audioUrl,
      'order_index': orderIndex,
      'is_favorite': isFavorite ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
    };
  }

  static List<String> _parseStringList(dynamic value) {
    if (value == null) return [];
    if (value is String) {
      if (value.isEmpty) return [];
      return value.split('|||');
    }
    if (value is List) {
      return value.map((e) => e.toString()).toList();
    }
    return [];
  }

  static String _stringListToJson(List<String> list) {
    return list.join('|||');
  }

  DuaModel copyWith({
    String? id,
    String? categoryId,
    String? title,
    String? arabicText,
    String? transliteration,
    String? englishTranslation,
    String? simpleExplanation,
    List<String>? benefits,
    List<String>? whenToRecite,
    List<String>? realLifeExamples,
    String? hadithReference,
    String? audioUrl,
    int? orderIndex,
    bool? isFavorite,
    DateTime? createdAt,
  }) {
    return DuaModel(
      id: id ?? this.id,
      categoryId: categoryId ?? this.categoryId,
      title: title ?? this.title,
      arabicText: arabicText ?? this.arabicText,
      transliteration: transliteration ?? this.transliteration,
      englishTranslation: englishTranslation ?? this.englishTranslation,
      simpleExplanation: simpleExplanation ?? this.simpleExplanation,
      benefits: benefits ?? this.benefits,
      whenToRecite: whenToRecite ?? this.whenToRecite,
      realLifeExamples: realLifeExamples ?? this.realLifeExamples,
      hadithReference: hadithReference ?? this.hadithReference,
      audioUrl: audioUrl ?? this.audioUrl,
      orderIndex: orderIndex ?? this.orderIndex,
      isFavorite: isFavorite ?? this.isFavorite,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        categoryId,
        title,
        arabicText,
        transliteration,
        englishTranslation,
        simpleExplanation,
        benefits,
        whenToRecite,
        realLifeExamples,
        hadithReference,
        audioUrl,
        orderIndex,
        isFavorite,
        createdAt,
      ];
}

/// Dua Progress Tracking Model
class DuaProgressModel extends Equatable {
  final String id;
  final String duaId;
  final DateTime lastRecited;
  final int recitationCount;
  final int streakDays;
  final bool isLearned;

  const DuaProgressModel({
    required this.id,
    required this.duaId,
    required this.lastRecited,
    required this.recitationCount,
    required this.streakDays,
    this.isLearned = false,
  });

  factory DuaProgressModel.fromJson(Map<String, dynamic> json) {
    return DuaProgressModel(
      id: json['id'] ?? '',
      duaId: json['dua_id'] ?? '',
      lastRecited: DateTime.parse(
        json['last_recited'] ?? DateTime.now().toIso8601String(),
      ),
      recitationCount: json['recitation_count'] ?? 0,
      streakDays: json['streak_days'] ?? 0,
      isLearned: json['is_learned'] == 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dua_id': duaId,
      'last_recited': lastRecited.toIso8601String(),
      'recitation_count': recitationCount,
      'streak_days': streakDays,
      'is_learned': isLearned ? 1 : 0,
    };
  }

  @override
  List<Object?> get props => [
        id,
        duaId,
        lastRecited,
        recitationCount,
        streakDays,
        isLearned,
      ];
}
