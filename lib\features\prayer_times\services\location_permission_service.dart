import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:injectable/injectable.dart';
import '../widgets/location_permission_dialog.dart';

@injectable
class LocationPermissionService {
  /// Check if location services are enabled and permissions are granted
  Future<LocationPermissionStatus> checkLocationStatus() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return LocationPermissionStatus.serviceDisabled;
      }

      // Check permission status
      LocationPermission permission = await Geolocator.checkPermission();

      switch (permission) {
        case LocationPermission.denied:
          return LocationPermissionStatus.denied;
        case LocationPermission.deniedForever:
          return LocationPermissionStatus.deniedForever;
        case LocationPermission.whileInUse:
        case LocationPermission.always:
          return LocationPermissionStatus.granted;
        default:
          return LocationPermissionStatus.denied;
      }
    } catch (e) {
      return LocationPermissionStatus.error;
    }
  }

  /// Request location permission with user-friendly dialog
  Future<bool> requestLocationPermission(BuildContext context) async {
    try {
      final status = await checkLocationStatus();

      // Check if context is still mounted after async operation
      if (!context.mounted) return false;

      switch (status) {
        case LocationPermissionStatus.granted:
          return true;

        case LocationPermissionStatus.serviceDisabled:
          if (context.mounted) {
            await _showLocationServiceDialog(context);
          }
          return false;

        case LocationPermissionStatus.deniedForever:
          if (context.mounted) {
            await _showOpenSettingsDialog(context);
          }
          return false;

        case LocationPermissionStatus.denied:
          if (context.mounted) {
            return await _showPermissionDialog(context);
          }
          return false;

        case LocationPermissionStatus.error:
          if (context.mounted) {
            await _showErrorDialog(context);
          }
          return false;
      }
    } catch (e) {
      if (context.mounted) {
        await _showErrorDialog(context);
      }
      return false;
    }
  }

  /// Show user-friendly permission dialog
  Future<bool> _showPermissionDialog(BuildContext context) async {
    bool permissionGranted = false;

    await LocationPermissionDialog.show(
      context,
      onPermissionGranted: () {
        permissionGranted = true;
      },
      onPermissionDenied: () {
        permissionGranted = false;
      },
    );

    return permissionGranted;
  }

  /// Show dialog when location services are disabled
  Future<void> _showLocationServiceDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.location_off, color: Colors.orange),
              SizedBox(width: 8),
              Expanded(child: Text('Location Services Disabled')),
            ],
          ),
          content: Text(
            'Please enable location services in your device settings to get accurate prayer times for your location.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await Geolocator.openLocationSettings();
              },
              child: Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  /// Show dialog when permission is permanently denied
  Future<void> _showOpenSettingsDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.settings, color: Colors.blue),
              SizedBox(width: 8),
              Expanded(child: Text('Permission Required')),
            ],
          ),
          content: Text(
            'Location permission has been permanently denied. Please enable it in app settings to get accurate prayer times.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await Geolocator.openAppSettings();
              },
              child: Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  /// Show error dialog
  Future<void> _showErrorDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.error, color: Colors.red),
              SizedBox(width: 8),
              Expanded(child: Text('Location Error')),
            ],
          ),
          content: Text(
            'There was an error accessing your location. Please check your device settings and try again.',
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Check if we should show permission rationale
  Future<bool> shouldShowPermissionRationale() async {
    try {
      final permission = await Geolocator.checkPermission();
      return permission == LocationPermission.denied;
    } catch (e) {
      return false;
    }
  }

  /// Get current location with permission handling (exact copy of working old code)
  Future<Position?> getCurrentLocationWithPermission(
    BuildContext context,
  ) async {
    try {
      // Exact same approach as your working old code
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw 'Location services are disabled';
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw 'Location permissions are denied';
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw 'Location permissions are permanently denied';
      }

      // Simple call like your working code - no complex settings
      return await Geolocator.getCurrentPosition();
    } catch (e) {
      // Show error dialog like your working code
      if (context.mounted) {
        _showLocationError(context, e.toString());
      }
      return null;
    }
  }

  // Show location error dialog (like your working code)
  void _showLocationError(BuildContext context, String error) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Location Access Required'),
            content: Text(
              'Prayer times are calculated based on your location. '
              'Please enable location services to get accurate prayer times.\n\n'
              'Error: $error',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('CANCEL'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // Retry
                  getCurrentLocationWithPermission(context);
                },
                child: const Text('RETRY'),
              ),
            ],
          ),
    );
  }
}

enum LocationPermissionStatus {
  granted,
  denied,
  deniedForever,
  serviceDisabled,
  error,
}
