name: noor_islamic_app
description: "A comprehensive Islamic app with prayer times, Qibla finder, Quran, Hadith, and AI chat features."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.8
  flutter_screenutil: ^5.9.3  # Responsive UI
  google_fonts: ^6.2.1        # Beautiful fonts (<PERSON><PERSON>, <PERSON><PERSON> Arabic, Inter, Poppins)
  flutter_svg: ^2.0.10+1      # SVG support
  lottie: ^3.1.2              # Animations
  shimmer: ^3.0.0             # Loading effects
  flutter_staggered_animations: ^1.1.1  # Staggered animations
  animated_text_kit: ^4.2.2   # Text animations

  # State Management & Architecture
  flutter_bloc: ^9.1.1        # State management
  bloc: ^9.0.0               # Core bloc package
  share_plus: ^10.0.2        # For sharing duas
  get_it: ^8.0.2              # Dependency injection
  injectable: ^2.4.4          # Code generation for DI
  equatable: ^2.0.5           # Value equality

  # Navigation & Routing
  go_router: ^16.0.0          # Modern routing

  # Theme & UI Enhancement
  dynamic_color: ^1.7.0       # Material You colors
  flex_color_scheme: ^8.2.0   # Advanced theming

  # Location & Prayer Times
  geolocator: ^14.0.2         # Location services
  geocoding: ^4.0.0           # Address from coordinates
  permission_handler: ^12.0.1 # Permissions
  adhan: ^2.0.0+1             # Prayer times calculation

  # Compass & Qibla
  flutter_compass: ^0.8.1     # Compass functionality
  sensors_plus: ^6.1.1        # Device sensors

  # Storage & Caching
  shared_preferences: ^2.3.2  # Simple storage
  hive: ^2.2.3                # Fast local database
  hive_flutter: ^1.1.0        # Hive Flutter integration
  path_provider: ^2.1.4       # File system paths

  # Network & API
  dio: ^5.7.0                 # HTTP client
  connectivity_plus: ^6.1.0   # Network connectivity
  cached_network_image: ^3.4.1 # Image caching

  # Islamic Content
  quran: ^1.2.0             # Quran text and metadata

  # Notifications & Time
  timezone: ^0.9.4            # Timezone handling (downgraded for compatibility)

  # Audio & Media
  just_audio: ^0.10.4         # Audio player
  audio_session: ^0.2.2      # Audio session management

  # Utilities
  intl: ^0.20.2               # Internationalization
  flutter_dotenv: ^5.1.0      # Environment variables for secure API keys

  package_info_plus: ^8.1.0   # App info
  device_info_plus: ^11.1.0   # Device info
  logger: ^2.6.0              # Professional logging framework
  hijri: ^3.0.0               # Hijri Islamic calendar

  # Prayer Times - World-class accurate calculation
  prayers_times: ^0.0.1-beta3 # Comprehensive Islamic prayer times (compatible version)
  vector_math: ^2.1.4
  flutter_map: ^8.1.1
  http: ^1.4.0
  latlong2: ^0.9.1

  # Quran - World-class accurate implementation
  sqflite: ^2.4.1              # Local database for offline Quran
  path: ^1.9.1                 # File path utilities
  audioplayers: ^6.1.0         # Audio playback for recitations
  scrollable_positioned_list: ^0.3.8  # Advanced scrolling for verses
  url_launcher: ^6.3.2
  dotenv: ^4.2.0
  google_generative_ai: ^0.4.7


dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.13       # Code generation runner
  injectable_generator: ^2.6.2 # DI code generation
  hive_generator: ^2.0.1      # Hive type adapters
  json_annotation: ^4.9.0     # JSON serialization
  json_serializable: ^6.8.0   # JSON code generation

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/audio/
    - assets/data/
    - .env


    