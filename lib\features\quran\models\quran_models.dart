import 'package:equatable/equatable.dart';

/// Surah (Chapter) model
class SurahModel extends Equatable {
  final int number;
  final String name;
  final String englishName;
  final String englishNameTranslation;
  final String revelationType; // Meccan or Medinan
  final int numberOfAyahs;
  final List<AyahModel> ayahs;

  const SurahModel({
    required this.number,
    required this.name,
    required this.englishName,
    required this.englishNameTranslation,
    required this.revelationType,
    required this.numberOfAyahs,
    this.ayahs = const [],
  });

  factory SurahModel.fromJson(Map<String, dynamic> json) {
    return SurahModel(
      number: json['number'] ?? 0,
      name: json['name'] ?? '',
      englishName: json['englishName'] ?? '',
      englishNameTranslation: json['englishNameTranslation'] ?? '',
      revelationType: json['revelationType'] ?? '',
      numberOfAyahs: json['numberOfAyahs'] ?? 0,
      ayahs:
          (json['ayahs'] as List<dynamic>?)
              ?.map((ayah) => AyahModel.fromJson(ayah))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name': name,
      'englishName': englishName,
      'englishNameTranslation': englishNameTranslation,
      'revelationType': revelationType,
      'numberOfAyahs': numberOfAyahs,
      'ayahs': ayahs.map((ayah) => ayah.toJson()).toList(),
    };
  }

  SurahModel copyWith({
    int? number,
    String? name,
    String? englishName,
    String? englishNameTranslation,
    String? revelationType,
    int? numberOfAyahs,
    List<AyahModel>? ayahs,
  }) {
    return SurahModel(
      number: number ?? this.number,
      name: name ?? this.name,
      englishName: englishName ?? this.englishName,
      englishNameTranslation:
          englishNameTranslation ?? this.englishNameTranslation,
      revelationType: revelationType ?? this.revelationType,
      numberOfAyahs: numberOfAyahs ?? this.numberOfAyahs,
      ayahs: ayahs ?? this.ayahs,
    );
  }

  @override
  List<Object?> get props => [
    number,
    name,
    englishName,
    englishNameTranslation,
    revelationType,
    numberOfAyahs,
    ayahs,
  ];
}

/// Ayah (Verse) model
class AyahModel extends Equatable {
  final int number;
  final String text; // Arabic text
  final int numberInSurah;
  final int juz;
  final int manzil;
  final int page;
  final int ruku;
  final int hizbQuarter;
  final bool sajda;
  final List<TranslationModel> translations;

  const AyahModel({
    required this.number,
    required this.text,
    required this.numberInSurah,
    required this.juz,
    required this.manzil,
    required this.page,
    required this.ruku,
    required this.hizbQuarter,
    this.sajda = false,
    this.translations = const [],
  });

  factory AyahModel.fromJson(Map<String, dynamic> json) {
    return AyahModel(
      number: json['number'] ?? 0,
      text: json['text'] ?? '',
      numberInSurah: json['numberInSurah'] ?? 0,
      juz: json['juz'] ?? 0,
      manzil: json['manzil'] ?? 0,
      page: json['page'] ?? 0,
      ruku: json['ruku'] ?? 0,
      hizbQuarter: json['hizbQuarter'] ?? 0,
      sajda: json['sajda'] is bool ? json['sajda'] : false,
      translations:
          (json['translations'] as List<dynamic>?)
              ?.map((translation) => TranslationModel.fromJson(translation))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'text': text,
      'numberInSurah': numberInSurah,
      'juz': juz,
      'manzil': manzil,
      'page': page,
      'ruku': ruku,
      'hizbQuarter': hizbQuarter,
      'sajda': sajda,
      'translations': translations.map((t) => t.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [
    number,
    text,
    numberInSurah,
    juz,
    manzil,
    page,
    ruku,
    hizbQuarter,
    sajda,
    translations,
  ];
}

/// Translation model for different languages
class TranslationModel extends Equatable {
  final int id;
  final String language;
  final String text;
  final String resourceName;

  const TranslationModel({
    required this.id,
    required this.language,
    required this.text,
    required this.resourceName,
  });

  factory TranslationModel.fromJson(Map<String, dynamic> json) {
    return TranslationModel(
      id: json['id'] ?? 0,
      language: json['language'] ?? '',
      text: json['text'] ?? '',
      resourceName: json['resourceName'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'language': language,
      'text': text,
      'resourceName': resourceName,
    };
  }

  @override
  List<Object?> get props => [id, language, text, resourceName];
}

/// Juz (Para) model
class JuzModel extends Equatable {
  final int number;
  final List<SurahModel> surahs;

  const JuzModel({required this.number, required this.surahs});

  @override
  List<Object?> get props => [number, surahs];
}

/// Reading preferences
class QuranReadingPreferences extends Equatable {
  final bool showArabic;
  final bool showTranslation;
  final bool showTransliteration;
  final double arabicFontSize;
  final double translationFontSize;
  final String arabicFont;
  final bool nightMode;
  final double lineSpacing;

  const QuranReadingPreferences({
    this.showArabic = true,
    this.showTranslation = false, // Arabic-only by default
    this.showTransliteration = false,
    this.arabicFontSize = 22.0, // Slightly smaller for better reading
    this.translationFontSize = 16.0,
    this.arabicFont = 'Amiri', // Beautiful Google Font for Arabic
    this.nightMode = false,
    this.lineSpacing = 1.8, // Better line spacing for Arabic
  });

  QuranReadingPreferences copyWith({
    bool? showArabic,
    bool? showTranslation,
    bool? showTransliteration,
    double? arabicFontSize,
    double? translationFontSize,
    String? arabicFont,
    bool? nightMode,
    double? lineSpacing,
  }) {
    return QuranReadingPreferences(
      showArabic: showArabic ?? this.showArabic,
      showTranslation: showTranslation ?? this.showTranslation,
      showTransliteration: showTransliteration ?? this.showTransliteration,
      arabicFontSize: arabicFontSize ?? this.arabicFontSize,
      translationFontSize: translationFontSize ?? this.translationFontSize,
      arabicFont: arabicFont ?? this.arabicFont,
      nightMode: nightMode ?? this.nightMode,
      lineSpacing: lineSpacing ?? this.lineSpacing,
    );
  }

  @override
  List<Object?> get props => [
    showArabic,
    showTranslation,
    showTransliteration,
    arabicFontSize,
    translationFontSize,
    arabicFont,
    nightMode,
    lineSpacing,
  ];
}

/// Bookmark model
class BookmarkModel extends Equatable {
  final String id;
  final int surahNumber;
  final int ayahNumber;
  final String surahName;
  final String note;
  final DateTime createdAt;

  const BookmarkModel({
    required this.id,
    required this.surahNumber,
    required this.ayahNumber,
    required this.surahName,
    this.note = '',
    required this.createdAt,
  });

  factory BookmarkModel.fromJson(Map<String, dynamic> json) {
    return BookmarkModel(
      id: json['id'] ?? '',
      surahNumber: json['surah_number'] ?? 0,
      ayahNumber: json['ayah_number'] ?? 0,
      surahName: json['surah_name'] ?? '',
      note: json['note'] ?? '',
      createdAt: DateTime.parse(
        json['created_at'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'surah_number': surahNumber,
      'ayah_number': ayahNumber,
      'surah_name': surahName,
      'note': note,
      'created_at': createdAt.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
    id,
    surahNumber,
    ayahNumber,
    surahName,
    note,
    createdAt,
  ];
}
