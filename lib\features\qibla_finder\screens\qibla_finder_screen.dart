import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../presentation/widgets/gradient_background.dart';
import '../cubit/qibla_cubit.dart';
import '../cubit/qibla_state.dart';
import '../models/qibla_model.dart';
import '../widgets/qibla_compass.dart';
import '../widgets/qibla_info_card.dart';
import '../widgets/qibla_loading_widget.dart';

class QiblaFinderScreen extends StatefulWidget {
  const QiblaFinderScreen({super.key});

  @override
  State<QiblaFinderScreen> createState() => _QiblaFinderScreenState();
}

class _QiblaFinderScreenState extends State<QiblaFinderScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // Start animations
    _fadeController.forward();
    _pulseController.repeat(reverse: true);

    // Initialize Qibla finder
    context.read<QiblaCubit>().initializeQibla();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: SafeArea(
          child: Column(
            children: [
              // Header
              _buildHeader(),

              // Main content
              Expanded(
                child: BlocBuilder<QiblaCubit, QiblaState>(
                  builder: (context, state) {
                    if (state is QiblaLoading) {
                      return QiblaLoadingWidget(
                        progress: state.progress,
                        message: state.message,
                      );
                    } else if (state is QiblaLocationServiceError) {
                      return _buildLocationServiceErrorWidget(state.message);
                    } else if (state is QiblaError) {
                      return _buildErrorWidget(state.message);
                    } else if (state is QiblaLoaded) {
                      return _buildQiblaContent(state.qiblaData);
                    }
                    return const QiblaLoadingWidget();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      child: Row(
        children: [
          // Back button
          Container(
            decoration: BoxDecoration(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.2)
                      : Colors.black.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: Icon(
                Icons.arrow_back_ios_new,
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                size: 20.sp,
              ),
            ),
          ),

          SizedBox(width: 16.w),

          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Qibla Finder',
                  style: AppTextStyles.headlineMedium.copyWith(
                    color:
                        Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black87,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                Text(
                  'Find the direction to Kaaba',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color:
                        Theme.of(context).brightness == Brightness.dark
                            ? Colors.white.withValues(alpha: 0.8)
                            : Colors.black.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),

          // Refresh button
          Container(
            decoration: BoxDecoration(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.2)
                      : Colors.black.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: IconButton(
              onPressed: () => context.read<QiblaCubit>().refreshLocation(),
              icon: Icon(
                Icons.refresh,
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                size: 20.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQiblaContent(QiblaModel qiblaData) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Column(
          children: [
            // Location info card
            QiblaInfoCard(qiblaData: qiblaData),

            SizedBox(height: 24.h),

            // Main compass
            Expanded(
              child: Center(
                child: QiblaCompass(
                  qiblaData: qiblaData,
                  pulseAnimation: _pulseAnimation,
                ),
              ),
            ),

            SizedBox(height: 24.h),

            // Status message
            _buildStatusMessage(qiblaData),

            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusMessage(QiblaModel qiblaData) {
    final bool isFacing = qiblaData.isFacingQibla;
    final double offset = qiblaData.offset.abs();

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      decoration: BoxDecoration(
        color:
            isFacing
                ? AppColors.primaryGreen.withValues(alpha: 0.1)
                : Theme.of(context).brightness == Brightness.dark
                ? Colors.white.withValues(alpha: 0.1)
                : Colors.black.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color:
              isFacing
                  ? AppColors.primaryGreen.withValues(alpha: 0.3)
                  : Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withValues(alpha: 0.2)
                  : Colors.black.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isFacing ? Icons.check_circle : Icons.explore,
            color:
                isFacing
                    ? AppColors.primaryGreen
                    : Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black87,
            size: 24.sp,
          ),

          SizedBox(width: 12.w),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isFacing ? 'Facing Qibla' : 'Adjust Direction',
                  style: AppTextStyles.titleMedium.copyWith(
                    color:
                        isFacing
                            ? AppColors.primaryGreen
                            : Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black87,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  isFacing
                      ? 'You are facing the correct direction'
                      : 'Turn ${offset.toStringAsFixed(0)}° ${qiblaData.offset > 0 ? 'right' : 'left'}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color:
                        isFacing
                            ? AppColors.primaryGreen.withValues(alpha: 0.8)
                            : Theme.of(context).brightness == Brightness.dark
                            ? Colors.white.withValues(alpha: 0.7)
                            : Colors.black.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64.sp,
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.7)
                      : Colors.black.withValues(alpha: 0.7),
            ),

            SizedBox(height: 16.h),

            Text(
              'Unable to Find Qibla',
              style: AppTextStyles.titleLarge.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 8.h),

            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.8)
                        : Colors.black.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 24.h),

            ElevatedButton(
              onPressed: () => context.read<QiblaCubit>().initializeQibla(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGreen,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Text(
                'Try Again',
                style: AppTextStyles.titleMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationServiceErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_off,
              size: 64.sp,
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.7)
                      : Colors.black.withValues(alpha: 0.7),
            ),

            SizedBox(height: 16.h),

            Text(
              'Location Access Needed',
              style: AppTextStyles.titleLarge.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 8.h),

            Text(
              message.contains('permission')
                  ? 'We need location permission to find the Qibla direction accurately.'
                  : 'Please turn on GPS/Location services to find the Qibla direction accurately.',
              style: AppTextStyles.bodyMedium.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.8)
                        : Colors.black.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 24.h),

            // Smart action button based on error type
            ElevatedButton.icon(
              onPressed: () async {
                if (message.contains('permission')) {
                  // For permission issues, try to request permission again
                  context.read<QiblaCubit>().initializeQibla();
                } else {
                  // For GPS/location service issues, open settings
                  await Geolocator.openLocationSettings();
                }
              },
              icon: Icon(
                message.contains('permission')
                    ? Icons.security
                    : Icons.settings,
                size: 20.sp,
              ),
              label: Text(
                message.contains('permission')
                    ? 'Allow Location Access'
                    : 'Open Location Settings',
                style: AppTextStyles.titleMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGreen,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
            ),

            SizedBox(height: 12.h),

            // Try Again button
            TextButton(
              onPressed: () => context.read<QiblaCubit>().initializeQibla(),
              child: Text(
                'Try Again',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.primaryGreen,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
