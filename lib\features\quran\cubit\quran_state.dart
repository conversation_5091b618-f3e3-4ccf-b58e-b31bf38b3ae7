import 'package:equatable/equatable.dart';
import '../models/quran_models.dart';

/// Base state for Quran feature
abstract class QuranState extends Equatable {
  const QuranState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class QuranInitial extends QuranState {}

/// Loading state with progress tracking
class QuranLoading extends QuranState {
  final double progress;
  final String message;

  const QuranLoading({this.progress = 0.0, this.message = 'Loading...'});

  @override
  List<Object?> get props => [progress, message];
}

/// Surahs list loaded
class QuranSurahsLoaded extends QuranState {
  final List<SurahModel> surahs;
  final QuranReadingPreferences preferences;

  const QuranSurahsLoaded({required this.surahs, required this.preferences});

  QuranSurahsLoaded copyWith({
    List<SurahModel>? surahs,
    QuranReadingPreferences? preferences,
  }) {
    return QuranSurahsLoaded(
      surahs: surahs ?? this.surahs,
      preferences: preferences ?? this.preferences,
    );
  }

  @override
  List<Object?> get props => [surahs, preferences];
}

/// Specific Surah loaded for reading
class QuranSurahReading extends QuranState {
  final SurahModel surah;
  final QuranReadingPreferences preferences;
  final int? currentAyahIndex;
  final List<BookmarkModel> bookmarks;

  const QuranSurahReading({
    required this.surah,
    required this.preferences,
    this.currentAyahIndex,
    this.bookmarks = const [],
  });

  QuranSurahReading copyWith({
    SurahModel? surah,
    QuranReadingPreferences? preferences,
    int? currentAyahIndex,
    List<BookmarkModel>? bookmarks,
  }) {
    return QuranSurahReading(
      surah: surah ?? this.surah,
      preferences: preferences ?? this.preferences,
      currentAyahIndex: currentAyahIndex ?? this.currentAyahIndex,
      bookmarks: bookmarks ?? this.bookmarks,
    );
  }

  @override
  List<Object?> get props => [surah, preferences, currentAyahIndex, bookmarks];
}

/// Search results state
class QuranSearchResults extends QuranState {
  final List<AyahModel> results;
  final String query;
  final QuranReadingPreferences preferences;

  const QuranSearchResults({
    required this.results,
    required this.query,
    required this.preferences,
  });

  @override
  List<Object?> get props => [results, query, preferences];
}

/// Bookmarks state
class QuranBookmarks extends QuranState {
  final List<BookmarkModel> bookmarks;
  final QuranReadingPreferences preferences;

  const QuranBookmarks({required this.bookmarks, required this.preferences});

  @override
  List<Object?> get props => [bookmarks, preferences];
}

/// Settings/Preferences state
class QuranSettings extends QuranState {
  final QuranReadingPreferences preferences;
  final List<Map<String, dynamic>> availableTranslations;

  const QuranSettings({
    required this.preferences,
    required this.availableTranslations,
  });

  QuranSettings copyWith({
    QuranReadingPreferences? preferences,
    List<Map<String, dynamic>>? availableTranslations,
  }) {
    return QuranSettings(
      preferences: preferences ?? this.preferences,
      availableTranslations:
          availableTranslations ?? this.availableTranslations,
    );
  }

  @override
  List<Object?> get props => [preferences, availableTranslations];
}

/// Error state
class QuranError extends QuranState {
  final String message;

  const QuranError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Network error state (API unavailable)
class QuranNetworkError extends QuranState {
  final String message;

  const QuranNetworkError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Juz (Para) state
class QuranJuzLoaded extends QuranState {
  final JuzModel juz;
  final QuranReadingPreferences preferences;

  const QuranJuzLoaded({required this.juz, required this.preferences});

  @override
  List<Object?> get props => [juz, preferences];
}
