import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:injectable/injectable.dart';
import '../../../core/constants/app_constants.dart';

part 'theme_state.dart';

@injectable
class ThemeCubit extends Cubit<ThemeState> {
  final SharedPreferences _prefs;

  ThemeCubit(this._prefs) : super(ThemeState.initial()) {
    _loadTheme();
  }

  void _loadTheme() {
    final savedTheme = _prefs.getString(AppConstants.themeKey);
    if (savedTheme != null) {
      final themeMode = ThemeMode.values.firstWhere(
        (mode) => mode.toString() == savedTheme,
        orElse: () => ThemeMode.system,
      );
      emit(state.copyWith(themeMode: themeMode));
    }
  }

  void toggleTheme() {
    final newThemeMode =
        state.themeMode == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;

    // Emit immediately for smooth transition
    emit(state.copyWith(themeMode: newThemeMode));

    // Save to preferences asynchronously
    _saveThemePreference(newThemeMode);
  }

  Future<void> _saveThemePreference(ThemeMode themeMode) async {
    await _prefs.setString(AppConstants.themeKey, themeMode.toString());
  }

  Future<void> setThemeMode(ThemeMode themeMode) async {
    await _prefs.setString(AppConstants.themeKey, themeMode.toString());
    emit(state.copyWith(themeMode: themeMode));
  }
}
