import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:noor_islamic_app/features/allah_names/data/models/allah_name_model.dart';
import 'package:noor_islamic_app/features/allah_names/domain/allah_name.dart';
import 'package:noor_islamic_app/features/allah_names/domain/allah_names_repository.dart';

class AllahNamesRepositoryImpl implements AllahNamesRepository {
  @override
  Future<List<AllahName>> getAllahNames() async {
    final response = await rootBundle.loadString('assets/data/asma_ul_husna.json');
    final data = json.decode(response) as List;
    return data
        .map<AllahName>((name) => AllahNameModel.fromJson(name).toEntity())
        .toList();
  }
}
