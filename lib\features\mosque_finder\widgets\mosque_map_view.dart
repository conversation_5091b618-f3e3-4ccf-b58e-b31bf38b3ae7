import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/mosque_model.dart';

class MosqueMapView extends StatefulWidget {
  final List<MosqueModel> mosques;
  final double? userLatitude;
  final double? userLongitude;
  final Function(MosqueModel) onMosqueTap;

  const MosqueMapView({
    super.key,
    required this.mosques,
    this.userLatitude,
    this.userLongitude,
    required this.onMosqueTap,
  });

  @override
  State<MosqueMapView> createState() => _MosqueMapViewState();
}

class _MosqueMapViewState extends State<MosqueMapView>
    with TickerProviderStateMixin {
  late MapController _mapController;
  MosqueModel? _selectedMosque;
  late AnimationController _markerController;
  late Animation<double> _markerAnimation;

  @override
  void initState() {
    super.initState();
    _mapController = MapController();
    
    _markerController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _markerAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _markerController, curve: Curves.elasticOut),
    );
    
    _markerController.forward();
  }

  @override
  void dispose() {
    _markerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Calculate initial center and zoom
    LatLng center;
    double zoom = 13.0;

    if (widget.userLatitude != null && widget.userLongitude != null) {
      center = LatLng(widget.userLatitude!, widget.userLongitude!);
    } else if (widget.mosques.isNotEmpty) {
      center = LatLng(widget.mosques.first.latitude, widget.mosques.first.longitude);
    } else {
      center = const LatLng(0, 0);
    }

    return Stack(
      children: [
        // Map
        ClipRRect(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
          child: FlutterMap(
            mapController: _mapController,
            options: MapOptions(
              initialCenter: center,
              initialZoom: zoom,
              minZoom: 5.0,
              maxZoom: 18.0,
              onTap: (tapPosition, point) {
                setState(() {
                  _selectedMosque = null;
                });
              },
            ),
            children: [
              // Map tiles
              TileLayer(
                urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                userAgentPackageName: 'com.example.noor_islamic_app',
                maxZoom: 18,
              ),
              
              // Mosque markers
              MarkerLayer(
                markers: _buildMosqueMarkers(),
              ),
              
              // User location marker
              if (widget.userLatitude != null && widget.userLongitude != null)
                MarkerLayer(
                  markers: [
                    Marker(
                      point: LatLng(widget.userLatitude!, widget.userLongitude!),
                      width: 40.w,
                      height: 40.w,
                      child: AnimatedBuilder(
                        animation: _markerAnimation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _markerAnimation.value,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.blue,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.white,
                                  width: 3,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.blue.withValues(alpha: 0.3),
                                    blurRadius: 10.r,
                                    spreadRadius: 2.r,
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.my_location,
                                color: Colors.white,
                                size: 20.sp,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
        
        // Selected mosque info card
        if (_selectedMosque != null)
          Positioned(
            bottom: 20.h,
            left: 20.w,
            right: 20.w,
            child: _buildMosqueInfoCard(_selectedMosque!),
          ),
        
        // Map controls
        Positioned(
          top: 20.h,
          right: 20.w,
          child: Column(
            children: [
              _buildMapButton(
                icon: Icons.my_location,
                onTap: _centerOnUserLocation,
              ),
              SizedBox(height: 8.h),
              _buildMapButton(
                icon: Icons.zoom_in,
                onTap: () => _mapController.move(
                  _mapController.camera.center,
                  _mapController.camera.zoom + 1,
                ),
              ),
              SizedBox(height: 8.h),
              _buildMapButton(
                icon: Icons.zoom_out,
                onTap: () => _mapController.move(
                  _mapController.camera.center,
                  _mapController.camera.zoom - 1,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  List<Marker> _buildMosqueMarkers() {
    return widget.mosques.map((mosque) {
      final isSelected = _selectedMosque?.id == mosque.id;
      
      return Marker(
        point: LatLng(mosque.latitude, mosque.longitude),
        width: isSelected ? 60.w : 50.w,
        height: isSelected ? 60.w : 50.w,
        child: GestureDetector(
          onTap: () {
            setState(() {
              _selectedMosque = mosque;
            });
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              color: isSelected ? AppColors.secondaryGold : AppColors.primaryGreen,
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white,
                width: 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: (isSelected ? AppColors.secondaryGold : AppColors.primaryGreen)
                      .withValues(alpha: 0.4),
                  blurRadius: 15.r,
                  spreadRadius: 2.r,
                ),
              ],
            ),
            child: Icon(
              Icons.mosque,
              color: Colors.white,
              size: isSelected ? 30.sp : 25.sp,
            ),
          ),
        ),
      );
    }).toList();
  }

  Widget _buildMapButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Container(
      width: 44.w,
      height: 44.w,
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.black.withValues(alpha: 0.8)
            : Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12.r),
          child: Icon(
            icon,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white
                : Colors.black87,
            size: 20.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildMosqueInfoCard(MosqueModel mosque) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.black.withValues(alpha: 0.9)
            : Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 20.r,
            offset: Offset(0, 10.h),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      mosque.name,
                      style: AppTextStyles.titleMedium.copyWith(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black87,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      mosque.address,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white.withValues(alpha: 0.7)
                            : Colors.black.withValues(alpha: 0.6),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              if (mosque.distanceFromUser != null) ...[
                SizedBox(width: 12.w),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 8.w,
                    vertical: 4.h,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primaryGreen.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Text(
                    '${mosque.distanceFromUser!.toStringAsFixed(1)} km',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.primaryGreen,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ],
          ),
          
          SizedBox(height: 12.h),
          
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => widget.onMosqueTap(mosque),
                  icon: Icon(Icons.info_outline, size: 16.sp),
                  label: Text(
                    'Details',
                    style: AppTextStyles.bodySmall.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryGreen,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 8.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              IconButton(
                onPressed: () {
                  setState(() {
                    _selectedMosque = null;
                  });
                },
                icon: Icon(
                  Icons.close,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.7)
                      : Colors.black.withValues(alpha: 0.7),
                  size: 20.sp,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _centerOnUserLocation() {
    if (widget.userLatitude != null && widget.userLongitude != null) {
      _mapController.move(
        LatLng(widget.userLatitude!, widget.userLongitude!),
        15.0,
      );
    }
  }
}
