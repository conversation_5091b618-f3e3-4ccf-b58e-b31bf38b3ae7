import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/quran_models.dart';

class QuranContinuousReadingView extends StatefulWidget {
  final SurahModel surah;
  final QuranReadingPreferences preferences;
  final Function() onBackToSurahs;
  final Function(QuranReadingPreferences) onPreferencesChanged;

  const QuranContinuousReadingView({
    super.key,
    required this.surah,
    required this.preferences,
    required this.onBackToSurahs,
    required this.onPreferencesChanged,
  });

  @override
  State<QuranContinuousReadingView> createState() =>
      _QuranContinuousReadingViewState();
}

class _QuranContinuousReadingViewState
    extends State<QuranContinuousReadingView> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Main content
        Column(
          children: [
            // Compact header
            _buildCompactHeader(),

            // Continuous reading content
            Expanded(
              child: SingleChildScrollView(
                controller: _scrollController,
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Helpful reminder message
                    _buildReminderMessage(),

                    SizedBox(height: 16.h),

                    // Bismillah (except for Surah At-Tawbah)
                    if (widget.surah.number != 9) ...[
                      _buildBismillah(),
                      SizedBox(height: 24.h),
                    ],

                    // Continuous Quran text
                    _buildContinuousQuranText(),

                    // Bottom padding for font settings
                    SizedBox(height: 80.h),
                  ],
                ),
              ),
            ),
          ],
        ),

        // Floating font settings overlay
        if (_showingSettings)
          Positioned(
            bottom: 20.h,
            left: 16.w,
            right: 16.w,
            child: _buildFloatingFontSettings(),
          ),
      ],
    );
  }

  Widget _buildCompactHeader() {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.primaryGreen, AppColors.secondaryGold],
        ),
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGreen.withValues(alpha: 0.25),
            blurRadius: 15.r,
            offset: Offset(0, 4.h),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20.r,
            offset: Offset(0, 8.h),
          ),
        ],
      ),
      child: Row(
        children: [
          // Surah number badge
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.25),
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              '${widget.surah.number}',
              style: AppTextStyles.titleMedium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w700,
                fontSize: 16.sp,
              ),
            ),
          ),

          SizedBox(width: 12.w),

          // Surah info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Arabic name
                Text(
                  widget.surah.name,
                  style: GoogleFonts.amiri(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                    fontSize: 18.sp,
                  ),
                  textDirection: TextDirection.rtl,
                ),

                // English name and info
                Text(
                  '${widget.surah.englishName} • ${widget.surah.numberOfAyahs} Ayahs',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 11.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // Font settings button
          Container(
            width: 36.w,
            height: 36.w,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withValues(alpha: 0.25),
                  Colors.white.withValues(alpha: 0.15),
                ],
              ),
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  // Show font settings bottom sheet
                  _showFontSettingsSheet();
                },
                borderRadius: BorderRadius.circular(8.r),
                child: Icon(Icons.tune, color: Colors.white, size: 18.sp),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReminderMessage() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            AppColors.primaryGreen.withValues(alpha: 0.08),
            AppColors.secondaryGold.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColors.primaryGreen.withValues(alpha: 0.15),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.lightbulb_outline,
            color: AppColors.primaryGreen,
            size: 18.sp,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              'Tip: Use the settings button (⚙️) to adjust font size and toggle translation',
              style: GoogleFonts.inter(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.8)
                        : Colors.black.withValues(alpha: 0.7),
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBismillah() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.primaryGreen.withValues(alpha: 0.08),
            AppColors.secondaryGold.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: AppColors.primaryGreen.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGreen.withValues(alpha: 0.1),
            blurRadius: 15.r,
            offset: Offset(0, 3.h),
          ),
        ],
      ),
      child: Text(
        'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        style: GoogleFonts.amiri(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black87,
          fontWeight: FontWeight.w600,
          fontSize: widget.preferences.arabicFontSize.sp,
          height: widget.preferences.lineSpacing,
        ),
        textDirection: TextDirection.rtl,
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildContinuousQuranText() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? Colors.white.withValues(alpha: 0.03)
                : Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withValues(alpha: 0.1)
                  : Colors.black.withValues(alpha: 0.05),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 10.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child:
          widget.preferences.showTranslation
              ? _buildBoxStyleLayout()
              : _buildArabicOnlyLayout(),
    );
  }

  List<TextSpan> _buildContinuousTextSpans() {
    final List<TextSpan> spans = [];

    for (int i = 0; i < widget.surah.ayahs.length; i++) {
      final ayah = widget.surah.ayahs[i];

      // Add ayah text
      spans.add(
        TextSpan(
          text: ayah.text,
          style: GoogleFonts.amiri(
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black87,
            fontWeight: FontWeight.w500,
            fontSize: widget.preferences.arabicFontSize.sp,
            height: widget.preferences.lineSpacing,
          ),
        ),
      );

      // Add ayah number in a circle
      spans.add(TextSpan(text: ' ', style: TextStyle(fontSize: 4.sp)));

      spans.add(
        TextSpan(
          text: _getArabicNumber(ayah.numberInSurah),
          style: GoogleFonts.amiri(
            color: AppColors.primaryGreen,
            fontWeight: FontWeight.w600,
            fontSize: (widget.preferences.arabicFontSize * 0.6).sp,
          ),
        ),
      );

      // Add space between ayahs (except for the last one)
      if (i < widget.surah.ayahs.length - 1) {
        spans.add(TextSpan(text: ' ', style: TextStyle(fontSize: 8.sp)));
      }
    }

    return spans;
  }

  String _getArabicNumber(int number) {
    // Convert to Arabic-Indic numerals and add decorative circle
    const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    String arabicNumber = '';

    for (int digit in number.toString().split('').map(int.parse)) {
      arabicNumber += arabicNumerals[digit];
    }

    return '﴿$arabicNumber﴾'; // Decorative brackets around the number
  }

  Widget _buildArabicOnlyLayout() {
    return RichText(
      textDirection: TextDirection.rtl,
      textAlign: TextAlign.justify,
      text: TextSpan(children: _buildContinuousTextSpans()),
    );
  }

  Widget _buildBoxStyleLayout() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: widget.surah.ayahs.length,
      itemBuilder: (context, index) => _buildAyahBox(widget.surah.ayahs[index]),
    );
  }

  Widget _buildAyahBox(AyahModel ayah) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(14.w),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? Colors.white.withValues(alpha: 0.02)
                : Colors.grey[50],
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withValues(alpha: 0.06)
                  : AppColors.primaryGreen.withValues(alpha: 0.08),
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Ayah number
          Row(
            children: [
              Container(
                width: 28.w,
                height: 28.w,
                decoration: BoxDecoration(
                  color: AppColors.primaryGreen,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    ayah.numberInSurah.toString(),
                    style: GoogleFonts.inter(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 12.sp,
                    ),
                  ),
                ),
              ),
              const Spacer(),
            ],
          ),

          SizedBox(height: 12.h),

          // Arabic text
          Text(
            ayah.text,
            style: GoogleFonts.amiri(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black87,
              fontWeight: FontWeight.w500,
              fontSize: widget.preferences.arabicFontSize.sp,
              height: widget.preferences.lineSpacing,
            ),
            textDirection: TextDirection.rtl,
            textAlign: TextAlign.right,
          ),

          // Translation (if available)
          if (ayah.translations.isNotEmpty) ...[
            SizedBox(height: 10.h),
            Container(
              padding: EdgeInsets.all(10.w),
              decoration: BoxDecoration(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.03)
                        : AppColors.primaryGreen.withValues(alpha: 0.03),
                borderRadius: BorderRadius.circular(6.r),
              ),
              child: Text(
                ayah.translations.first.text,
                style: GoogleFonts.inter(
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.85)
                          : Colors.black.withValues(alpha: 0.75),
                  fontSize: widget.preferences.translationFontSize.sp,
                  height: 1.5,
                ),
                textAlign: TextAlign.left,
              ),
            ),
          ],
        ],
      ),
    );
  }

  bool _showingSettings = false;

  void _showFontSettingsSheet() {
    setState(() {
      _showingSettings = true;
    });
  }

  void _hideFontSettingsSheet() {
    setState(() {
      _showingSettings = false;
    });
  }

  Widget _buildFloatingFontSettings() {
    return GestureDetector(
      onTap: () {}, // Prevent tap-through
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: (Theme.of(context).brightness == Brightness.dark
                  ? Colors.black
                  : Colors.white)
              .withValues(alpha: 0.92), // More opaque for better readability
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(
            color: AppColors.primaryGreen.withValues(alpha: 0.3),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.25),
              blurRadius: 25.r,
              offset: Offset(0, 12.h),
            ),
            BoxShadow(
              color: AppColors.primaryGreen.withValues(alpha: 0.1),
              blurRadius: 40.r,
              offset: Offset(0, 8.h),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with close button
            Row(
              children: [
                Icon(Icons.tune, color: AppColors.primaryGreen, size: 20.sp),
                SizedBox(width: 8.w),
                Text(
                  'Reading Settings',
                  style: AppTextStyles.titleMedium.copyWith(
                    color:
                        Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black87,
                    fontWeight: FontWeight.w600,
                    fontSize: 16.sp,
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: _hideFontSettingsSheet,
                  child: Container(
                    padding: EdgeInsets.all(6.w),
                    decoration: BoxDecoration(
                      color: AppColors.primaryGreen.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(
                      Icons.close,
                      color: AppColors.primaryGreen,
                      size: 18.sp,
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: 16.h),

            // Font Size
            _buildCompactFontSizeSlider(),

            SizedBox(height: 16.h),

            // Line Spacing
            _buildCompactLineSpacingSlider(),

            SizedBox(height: 16.h),

            // Show Translation
            _buildCompactTranslationToggle(),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactFontSizeSlider() {
    return StatefulBuilder(
      builder: (context, setState) {
        double currentValue = widget.preferences.arabicFontSize;

        return Row(
          children: [
            Text(
              'Font Size',
              style: AppTextStyles.bodyMedium.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                fontWeight: FontWeight.w500,
                fontSize: 14.sp,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: AppColors.primaryGreen,
                  inactiveTrackColor: AppColors.primaryGreen.withValues(
                    alpha: 0.3,
                  ),
                  thumbColor: AppColors.primaryGreen,
                  overlayColor: AppColors.primaryGreen.withValues(alpha: 0.2),
                  trackHeight: 3.h,
                  thumbShape: RoundSliderThumbShape(enabledThumbRadius: 7.r),
                  overlayShape: RoundSliderOverlayShape(overlayRadius: 14.r),
                ),
                child: Slider(
                  value: currentValue,
                  min: 16.0,
                  max: 32.0,
                  divisions: 16,
                  onChanged: (value) {
                    setState(() {
                      currentValue = value;
                    });
                    // Update immediately for smooth dragging
                    widget.onPreferencesChanged(
                      widget.preferences.copyWith(arabicFontSize: value),
                    );
                  },
                ),
              ),
            ),
            Container(
              width: 36.w,
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 4.h),
              decoration: BoxDecoration(
                color: AppColors.primaryGreen.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: AppColors.primaryGreen.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Text(
                currentValue.toInt().toString(),
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.primaryGreen,
                  fontWeight: FontWeight.w700,
                  fontSize: 13.sp,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCompactLineSpacingSlider() {
    return StatefulBuilder(
      builder: (context, setState) {
        double currentValue = widget.preferences.lineSpacing;

        return Row(
          children: [
            Text(
              'Line Spacing',
              style: AppTextStyles.bodyMedium.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                fontWeight: FontWeight.w500,
                fontSize: 14.sp,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: AppColors.primaryGreen,
                  inactiveTrackColor: AppColors.primaryGreen.withValues(
                    alpha: 0.3,
                  ),
                  thumbColor: AppColors.primaryGreen,
                  overlayColor: AppColors.primaryGreen.withValues(alpha: 0.2),
                  trackHeight: 3.h,
                  thumbShape: RoundSliderThumbShape(enabledThumbRadius: 7.r),
                  overlayShape: RoundSliderOverlayShape(overlayRadius: 14.r),
                ),
                child: Slider(
                  value: currentValue,
                  min: 1.2,
                  max: 2.5,
                  divisions: 13,
                  onChanged: (value) {
                    setState(() {
                      currentValue = value;
                    });
                    // Update immediately for smooth dragging
                    widget.onPreferencesChanged(
                      widget.preferences.copyWith(lineSpacing: value),
                    );
                  },
                ),
              ),
            ),
            Container(
              width: 36.w,
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 4.h),
              decoration: BoxDecoration(
                color: AppColors.primaryGreen.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: AppColors.primaryGreen.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Text(
                currentValue.toStringAsFixed(1),
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.primaryGreen,
                  fontWeight: FontWeight.w700,
                  fontSize: 13.sp,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCompactTranslationToggle() {
    return Row(
      children: [
        Expanded(
          child: Text(
            'Show Translation',
            style: AppTextStyles.bodyMedium.copyWith(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black87,
              fontWeight: FontWeight.w500,
              fontSize: 14.sp,
            ),
          ),
        ),
        Switch.adaptive(
          value: widget.preferences.showTranslation,
          onChanged: (value) {
            // Update the preference immediately - no need to close overlay
            widget.onPreferencesChanged(
              widget.preferences.copyWith(showTranslation: value),
            );
          },
          activeColor: AppColors.primaryGreen,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      ],
    );
  }
}
