import 'package:equatable/equatable.dart';
import '../../../core/services/ai_service.dart';

/// Base state for AI Chat feature
abstract class AiChatState extends Equatable {
  const AiChatState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class AiChatInitial extends AiChatState {}

/// Loading state
class AiChatLoading extends AiChatState {
  final String message;

  const AiChatLoading({this.message = 'Initializing AI...'});

  @override
  List<Object?> get props => [message];
}

/// Chat ready state with messages
class AiChatLoaded extends AiChatState {
  final List<ChatMessage> messages;
  final bool isTyping;
  final String? currentStreamingMessage;
  final bool isFirstTime;

  const AiChatLoaded({
    required this.messages,
    this.isTyping = false,
    this.currentStreamingMessage,
    this.isFirstTime = false,
  });

  @override
  List<Object?> get props => [messages, isTyping, currentStreamingMessage, isFirstTime];

  AiChatLoaded copyWith({
    List<ChatMessage>? messages,
    bool? isTyping,
    String? currentStreamingMessage,
    bool? isFirstTime,
  }) {
    return AiChatLoaded(
      messages: messages ?? this.messages,
      isTyping: isTyping ?? this.isTyping,
      currentStreamingMessage: currentStreamingMessage ?? this.currentStreamingMessage,
      isFirstTime: isFirstTime ?? this.isFirstTime,
    );
  }
}

/// AI is thinking/processing
class AiChatThinking extends AiChatState {
  final List<ChatMessage> messages;
  final String thinkingMessage;

  const AiChatThinking({
    required this.messages,
    this.thinkingMessage = 'Sheikh AI is reflecting on your question...',
  });

  @override
  List<Object?> get props => [messages, thinkingMessage];
}

/// AI is streaming response
class AiChatStreaming extends AiChatState {
  final List<ChatMessage> messages;
  final String streamingContent;
  final ChatMessage userMessage;

  const AiChatStreaming({
    required this.messages,
    required this.streamingContent,
    required this.userMessage,
  });

  @override
  List<Object?> get props => [messages, streamingContent, userMessage];
}

/// Error state
class AiChatError extends AiChatState {
  final String message;
  final List<ChatMessage> messages;

  const AiChatError({
    required this.message,
    this.messages = const [],
  });

  @override
  List<Object?> get props => [message, messages];
}

/// Network error state
class AiChatNetworkError extends AiChatState {
  final String message;
  final List<ChatMessage> messages;

  const AiChatNetworkError({
    required this.message,
    this.messages = const [],
  });

  @override
  List<Object?> get props => [message, messages];
}

/// Message sent successfully
class AiChatMessageSent extends AiChatState {
  final List<ChatMessage> messages;

  const AiChatMessageSent({required this.messages});

  @override
  List<Object?> get props => [messages];
}
