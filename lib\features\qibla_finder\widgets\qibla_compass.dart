import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:math' as math;
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/qibla_model.dart';

class QiblaCompass extends StatefulWidget {
  final QiblaModel qiblaData;
  final Animation<double> pulseAnimation;

  const QiblaCompass({
    super.key,
    required this.qiblaData,
    required this.pulseAnimation,
  });

  @override
  State<QiblaCompass> createState() => _QiblaCompassState();
}

class _QiblaCompassState extends State<QiblaCompass>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();

    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.easeInOut),
    );

    _rotationController.forward();
  }

  @override
  void dispose() {
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_rotationAnimation, widget.pulseAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale:
              widget.qiblaData.isFacingQibla
                  ? widget.pulseAnimation.value
                  : 1.0,
          child: SizedBox(
            width: 280.w,
            height: 280.w,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Outer compass ring
                _buildCompassRing(),

                // Direction markers
                _buildDirectionMarkers(),

                // Qibla indicator
                _buildQiblaIndicator(),

                // Device direction needle
                _buildDeviceNeedle(),

                // Center circle
                _buildCenterCircle(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCompassRing() {
    return Container(
      width: 280.w,
      height: 280.w,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            Colors.white.withValues(alpha: 0.1),
            Colors.white.withValues(alpha: 0.05),
            Colors.transparent,
          ],
          stops: const [0.7, 0.9, 1.0],
        ),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
    );
  }

  Widget _buildDirectionMarkers() {
    return SizedBox(
      width: 280.w,
      height: 280.w,
      child: Stack(
        children: [
          // Cardinal directions (N, E, S, W)
          for (int i = 0; i < 4; i++)
            Transform.rotate(
              angle: i * math.pi / 2,
              child: Align(
                alignment: Alignment.topCenter,
                child: Container(
                  margin: EdgeInsets.only(top: 8.h),
                  child: Text(
                    ['N', 'E', 'S', 'W'][i],
                    style: AppTextStyles.titleMedium.copyWith(
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black87,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
            ),

          // Degree markers
          for (int i = 0; i < 36; i++)
            Transform.rotate(
              angle: i * math.pi / 18, // Every 10 degrees
              child: Align(
                alignment: Alignment.topCenter,
                child: Container(
                  margin: EdgeInsets.only(top: 20.h),
                  width: 2.w,
                  height:
                      i % 9 == 0 ? 12.h : 8.h, // Longer marks every 90 degrees
                  decoration: BoxDecoration(
                    color:
                        Theme.of(context).brightness == Brightness.dark
                            ? Colors.white.withValues(alpha: 0.6)
                            : Colors.black.withValues(alpha: 0.4),
                    borderRadius: BorderRadius.circular(1.r),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildQiblaIndicator() {
    final double qiblaAngle =
        (widget.qiblaData.qiblaDirection - widget.qiblaData.currentHeading) *
        math.pi /
        180;

    return Transform.rotate(
      angle: qiblaAngle,
      child: Align(
        alignment: Alignment.topCenter,
        child: Container(
          margin: EdgeInsets.only(top: 40.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Kaaba icon
              Container(
                width: 32.w,
                height: 32.w,
                decoration: BoxDecoration(
                  color: AppColors.secondaryGold,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.secondaryGold.withValues(alpha: 0.5),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.location_on,
                  color: Colors.white,
                  size: 20.sp,
                ),
              ),

              SizedBox(height: 8.h),

              // Qibla text
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: AppColors.secondaryGold,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  'QIBLA',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                    fontSize: 10.sp,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDeviceNeedle() {
    return Container(
      width: 4.w,
      height: 100.h,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.primaryGreen,
            AppColors.primaryGreen.withValues(alpha: 0.3),
          ],
        ),
        borderRadius: BorderRadius.circular(2.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGreen.withValues(alpha: 0.5),
            blurRadius: 4,
            spreadRadius: 1,
          ),
        ],
      ),
    );
  }

  Widget _buildCenterCircle() {
    return Container(
      width: 60.w,
      height: 60.w,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            AppColors.primaryGreen,
            AppColors.primaryGreen.withValues(alpha: 0.8),
          ],
        ),
        border: Border.all(color: Colors.white, width: 3),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGreen.withValues(alpha: 0.3),
            blurRadius: 8,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Icon(Icons.navigation, color: Colors.white, size: 24.sp),
    );
  }
}
