import 'package:equatable/equatable.dart';

class AllahName extends Equatable {
  final int number;
  final String name;
  final String transliteration;
  final String meaning;
  final String? explanation;
  final String? quranicVerse; // e.g., '<PERSON><PERSON>, 1:3'
  final String? reflection;
  final String? supplicationBenefit;

  const AllahName({
    required this.number,
    required this.name,
    required this.transliteration,
    required this.meaning,
    required this.explanation,
    this.quranicVerse,
    this.reflection,
    this.supplicationBenefit,
  });

  @override
  List<Object?> get props => [
        number,
        name,
        transliteration,
        meaning,
        explanation,
        quranicVerse,
        reflection,
        supplicationBenefit,
      ];
}
