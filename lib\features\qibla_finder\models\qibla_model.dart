class QiblaModel {
  final double qiblaDirection;
  final double currentHeading;
  final double distanceTo<PERSON>aaba;
  final double magneticDeclination;
  final String locationName;
  final double latitude;
  final double longitude;
  final bool isFacingQibla;
  final double offset;
  final String directionDescription;
  final DateTime lastUpdated;

  const QiblaModel({
    required this.qiblaDirection,
    required this.currentHeading,
    required this.distanceToKaaba,
    required this.magneticDeclination,
    required this.locationName,
    required this.latitude,
    required this.longitude,
    required this.isFacingQibla,
    required this.offset,
    required this.directionDescription,
    required this.lastUpdated,
  });

  QiblaModel copyWith({
    double? qiblaDirection,
    double? currentHeading,
    double? distanceToKaaba,
    double? magneticDeclination,
    String? locationName,
    double? latitude,
    double? longitude,
    bool? isFacingQibla,
    double? offset,
    String? directionDescription,
    DateTime? lastUpdated,
  }) {
    return QiblaModel(
      qiblaDirection: qiblaDirection ?? this.qiblaDirection,
      currentHeading: currentHeading ?? this.currentHeading,
      distanceToKaaba: distanceToKaaba ?? this.distanceToKaaba,
      magneticDeclination: magneticDeclination ?? this.magneticDeclination,
      locationName: locationName ?? this.locationName,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      isFacingQibla: isFacingQibla ?? this.isFacingQibla,
      offset: offset ?? this.offset,
      directionDescription: directionDescription ?? this.directionDescription,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  String toString() {
    return 'QiblaModel(qiblaDirection: $qiblaDirection, currentHeading: $currentHeading, '
           'distanceToKaaba: $distanceToKaaba, locationName: $locationName, '
           'isFacingQibla: $isFacingQibla, offset: $offset)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is QiblaModel &&
      other.qiblaDirection == qiblaDirection &&
      other.currentHeading == currentHeading &&
      other.distanceToKaaba == distanceToKaaba &&
      other.magneticDeclination == magneticDeclination &&
      other.locationName == locationName &&
      other.latitude == latitude &&
      other.longitude == longitude &&
      other.isFacingQibla == isFacingQibla &&
      other.offset == offset &&
      other.directionDescription == directionDescription;
  }

  @override
  int get hashCode {
    return qiblaDirection.hashCode ^
      currentHeading.hashCode ^
      distanceToKaaba.hashCode ^
      magneticDeclination.hashCode ^
      locationName.hashCode ^
      latitude.hashCode ^
      longitude.hashCode ^
      isFacingQibla.hashCode ^
      offset.hashCode ^
      directionDescription.hashCode;
  }
}
