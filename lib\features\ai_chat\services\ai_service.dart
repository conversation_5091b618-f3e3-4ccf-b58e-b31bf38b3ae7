import 'dart:async';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../../../core/services/logger_service.dart';
import '../constants/ai_prompts.dart';
import '../models/chat_message.dart';

/// Premium AI Service for Islamic Knowledge using Gemini 2.5 Flash
class AiService {
  static final AiService _instance = AiService._internal();
  factory AiService() => _instance;
  AiService._internal();

  late final GenerativeModel _model;
  late final ChatSession _chatSession;
  bool _isInitialized = false;

  /// Initialize the AI service with Gemini 2.5 Flash
  Future<void> initialize() async {
    try {
      final apiKey = _getApiKey();
      
      // Initialize Gemini 2.5 Flash model with optimal settings
      _model = GenerativeModel(
        model: 'gemini-2.5-flash',
        apiKey: apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.7,        // Balanced creativity and accuracy
          topK: 40,               // Good diversity
          topP: 0.95,             // High quality responses
          maxOutputTokens: 2048,   // Sufficient for detailed Islamic explanations
        ),
        safetySettings: [
          SafetySetting(HarmCategory.harassment, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.high),
          SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.medium),
        ],
        systemInstruction: Content.system(AiPrompts.islamicScholarSystemPrompt),
      );

      // Create chat session for conversation continuity
      _chatSession = _model.startChat();
      _isInitialized = true;

      LoggerService.info('🤖 AI: Sheikh AI initialized successfully with Gemini 2.5 Flash');
    } catch (e) {
      LoggerService.error('❌ AI: Initialization failed: $e');
      rethrow;
    }
  }

  /// Get API key from environment variables
  String _getApiKey() {
    final key = dotenv.env['GEMINI_API_KEY'];
    if (key == null || key.isEmpty) {
      throw Exception('GEMINI_API_KEY not found in environment variables. Please check your .env file.');
    }
    return key;
  }

  /// Send message and get streaming response
  Stream<String> sendMessageStream(String message) async* {
    if (!_isInitialized) {
      throw Exception('AI Service not initialized. Call initialize() first.');
    }

    try {
      LoggerService.info('🤖 AI: Sending message to Sheikh AI');
      
      // Send message to chat session and get streaming response
      final response = _chatSession.sendMessageStream(Content.text(message));
      
      await for (final chunk in response) {
        final text = chunk.text;
        if (text != null && text.isNotEmpty) {
          yield text;
        }
      }

      LoggerService.info('🤖 AI: Response stream completed successfully');
    } catch (e) {
      LoggerService.error('❌ AI: Stream error: $e');
      yield AiPrompts.errorMessage;
    }
  }

  /// Send message and get complete response (non-streaming)
  Future<String> sendMessage(String message) async {
    if (!_isInitialized) {
      throw Exception('AI Service not initialized. Call initialize() first.');
    }

    try {
      LoggerService.info('🤖 AI: Sending message to Sheikh AI');
      
      final response = await _chatSession.sendMessage(Content.text(message));
      final text = response.text;
      
      if (text != null && text.isNotEmpty) {
        LoggerService.info('🤖 AI: Response received successfully');
        return text;
      } else {
        throw Exception('Empty response from AI service');
      }
    } catch (e) {
      LoggerService.error('❌ AI: Request error: $e');
      return AiPrompts.errorMessage;
    }
  }

  /// Reset chat session (clear conversation history)
  void resetChatSession() {
    if (_isInitialized) {
      _chatSession = _model.startChat();
      LoggerService.info('🤖 AI: Chat session reset successfully');
    }
  }

  /// Get chat history
  List<Content> getChatHistory() {
    if (!_isInitialized) return [];
    return _chatSession.history;
  }

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Get model information
  String get modelInfo => 'Gemini 2.5 Flash - Islamic Knowledge Expert';
}
