part of 'allah_names_bloc.dart';

abstract class AllahNamesState extends Equatable {
  const AllahNamesState();

  @override
  List<Object> get props => [];
}

class AllahNamesInitial extends AllahNamesState {}

class AllahNamesLoading extends AllahNamesState {}

class AllahNamesLoaded extends AllahNamesState {
  final List<AllahName> names;

  const AllahNamesLoaded(this.names);

  @override
  List<Object> get props => [names];
}

class AllahNamesError extends AllahNamesState {
  final String message;

  const AllahNamesError(this.message);

  @override
  List<Object> get props => [message];
}
