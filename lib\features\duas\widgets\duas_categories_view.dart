import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/dua_models.dart';
import '../cubit/duas_cubit.dart';

class DuasCategoriesView extends StatelessWidget {
  final List<DuaCategoryModel> categories;
  final List<DuaModel> favoriteDuas;
  final List<DuaModel> recentDuas;

  const DuasCategoriesView({
    super.key,
    required this.categories,
    required this.favoriteDuas,
    required this.recentDuas,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      key: const PageStorageKey<String>('duas_categories_scroll'),
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Quick Access Section
          if (favoriteDuas.isNotEmpty || recentDuas.isNotEmpty) ...[
            _buildQuickAccessSection(context),
            SizedBox(height: 24.h),
          ],

          // Categories Section
          Text(
            'Dua Categories',
            style: AppTextStyles.headlineSmall.copyWith(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black87,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),

          // Categories Grid
          _buildCategoriesGrid(context),

          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  Widget _buildQuickAccessSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Access',
          style: AppTextStyles.headlineSmall.copyWith(
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black87,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),

        // Favorites and Recent Row
        Row(
          children: [
            // Favorites
            if (favoriteDuas.isNotEmpty) ...[
              Expanded(
                child: _buildQuickAccessCard(
                  context,
                  title: 'Favorites',
                  subtitle: '${favoriteDuas.length} duas',
                  icon: Icons.favorite,
                  color: Colors.red,
                  onTap: () => context.read<DuasCubit>().showFavorites(),
                ),
              ),
              SizedBox(width: 12.w),
            ],

            // Recent
            if (recentDuas.isNotEmpty) ...[
              Expanded(
                child: _buildQuickAccessCard(
                  context,
                  title: 'Recent',
                  subtitle: '${recentDuas.length} duas',
                  icon: Icons.history,
                  color: Colors.blue,
                  onTap: () => context.read<DuasCubit>().showRecent(),
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildQuickAccessCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16.r),
        child: Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: isDarkMode ? 0.2 : 0.06),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(icon, color: color, size: 24.sp),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTextStyles.labelLarge.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      subtitle,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoriesGrid(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing:
            16.w, // Increased spacing for better visual separation
        mainAxisSpacing: 16.h, // Increased spacing for better visual separation
        childAspectRatio: 0.8, // Optimized ratio for better content fit
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return _buildCategoryCard(context, category);
      },
    );
  }

  Widget _buildCategoryCard(BuildContext context, DuaCategoryModel category) {
    final color = Color(int.parse(category.colorHex.replaceFirst('#', '0xFF')));
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Enhanced color adjustment for better dark mode visibility
    final displayColor = isDarkMode ? _adjustColorForDarkMode(color) : color;
    final textColor = isDarkMode ? Colors.white : Colors.black87;
    final subtitleColor = isDarkMode ? Colors.grey[300] : Colors.grey[600];

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => context.read<DuasCubit>().loadCategoryDuas(category),
          borderRadius: BorderRadius.circular(20.r),
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(20.r),
              border: Border.all(
                color: displayColor.withValues(alpha: isDarkMode ? 0.4 : 0.2),
                width: isDarkMode ? 2.0 : 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: displayColor.withValues(
                    alpha: isDarkMode ? 0.25 : 0.15,
                  ),
                  blurRadius: isDarkMode ? 16 : 12,
                  offset: const Offset(0, 6),
                  spreadRadius: isDarkMode ? 2 : 0,
                ),
                BoxShadow(
                  color: Colors.black.withValues(
                    alpha: isDarkMode ? 0.4 : 0.08,
                  ),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Header with icon and count
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: displayColor.withValues(
                      alpha: isDarkMode ? 0.25 : 0.15,
                    ),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20.r),
                      topRight: Radius.circular(20.r),
                    ),
                  ),
                  child: Column(
                    children: [
                      // Icon placeholder (will add actual icons later)
                      Container(
                        width: 48.w,
                        height: 48.h,
                        decoration: BoxDecoration(
                          color: displayColor.withValues(alpha: 0.25),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Icon(
                          _getCategoryIcon(category.id),
                          color: displayColor,
                          size: 28.sp,
                        ),
                      ),
                    ],
                  ),
                ),

                // Content
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(12.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          category.name,
                          style: AppTextStyles.labelMedium.copyWith(
                            fontWeight: FontWeight.bold,
                            fontSize: 14.sp,
                            color: textColor,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 6.h),
                        Expanded(
                          child: Text(
                            category.description,
                            style: AppTextStyles.bodySmall.copyWith(
                              color: subtitleColor,
                              height: 1.3,
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 4,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String categoryId) {
    switch (categoryId) {
      case 'morning':
        return Icons.wb_sunny;
      case 'evening':
        return Icons.nights_stay;
      case 'night':
        return Icons.bedtime;
      case 'quranic':
        return Icons.menu_book;
      case 'forgiveness':
        return Icons.favorite;
      case 'protection':
        return Icons.shield;
      case 'paradise':
        return Icons.cloud;
      case 'travel':
        return Icons.flight;
      case 'illness':
        return Icons.healing;
      case 'marriage':
        return Icons.favorite_border;
      case 'children':
        return Icons.child_care;
      case 'wealth':
        return Icons.attach_money;
      case 'work':
        return Icons.work;
      case 'knowledge':
        return Icons.school;
      case 'anxiety':
        return Icons.self_improvement;
      case 'daily_life':
        return Icons.home;
      default:
        return Icons.menu_book;
    }
  }

  /// Adjust color brightness for dark mode visibility
  Color _adjustColorForDarkMode(Color color) {
    final hsl = HSLColor.fromColor(color);

    // Significantly increase lightness for better visibility in dark mode
    final adjustedLightness = (hsl.lightness + 0.5).clamp(0.4, 0.9);

    // Increase saturation for more vibrant colors in dark mode
    final adjustedSaturation = (hsl.saturation + 0.2).clamp(0.6, 1.0);

    return hsl
        .withLightness(adjustedLightness)
        .withSaturation(adjustedSaturation)
        .toColor();
  }
}
