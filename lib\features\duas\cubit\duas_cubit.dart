import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/services/logger_service.dart';
import '../models/dua_models.dart';
import '../services/duas_database_service.dart';
import '../services/duas_data_service.dart';
import 'duas_state.dart';

class DuasCubit extends Cubit<DuasState> {
  DuasCubit() : super(const DuasInitial());

  List<DuaCategoryModel> _categories = [];
  List<DuaModel> _favoriteDuas = [];
  List<DuaModel> _recentDuas = [];
  DuaCategoryModel? _currentCategory;

  /// Initialize duas feature
  Future<void> initialize() async {
    try {
      emit(const DuasLoading(progress: 0.1, message: 'Initializing Duas...'));

      // Force fresh initialization to ensure all duas are loaded
      emit(
        const DuasLoading(
          progress: 0.3,
          message: 'Setting up Duas database...',
        ),
      );
      await DuasDataService.initializeDuasData();

      emit(const DuasLoading(progress: 0.7, message: 'Loading categories...'));
      await loadCategories();

      LoggerService.duas('🤲 DUAS: Initialization completed successfully');
    } catch (e) {
      LoggerService.error('❌ DUAS: Error during initialization: $e');
      emit(DuasError('Failed to initialize Duas feature: ${e.toString()}'));
    }
  }

  /// Load all categories with favorites and recent duas
  Future<void> loadCategories() async {
    try {
      emit(const DuasLoading(progress: 0.5, message: 'Loading categories...'));

      _categories = await DuasDatabaseService.getCategories();
      _favoriteDuas = await DuasDatabaseService.getFavoriteDuas();

      // Get actual recent duas from database
      _recentDuas = await DuasDatabaseService.getRecentDuas();

      emit(
        DuasCategoriesLoaded(
          categories: _categories,
          favoriteDuas: _favoriteDuas,
          recentDuas: _recentDuas,
        ),
      );

      LoggerService.duas('🤲 DUAS: Loaded ${_categories.length} categories');
    } catch (e) {
      LoggerService.error('❌ DUAS: Error loading categories: $e');
      emit(DuasError('Failed to load categories: ${e.toString()}'));
    }
  }

  /// Load duas for specific category
  Future<void> loadCategoryDuas(DuaCategoryModel category) async {
    try {
      emit(const DuasLoading(progress: 0.5, message: 'Loading duas...'));

      final duas = await DuasDatabaseService.getDuasByCategory(category.id);

      emit(DuasCategoryDuasLoaded(category: category, duas: duas));

      LoggerService.duas(
        '🤲 DUAS: Loaded ${duas.length} duas for ${category.name}',
      );
    } catch (e) {
      LoggerService.error('❌ DUAS: Error loading category duas: $e');
      emit(DuasError('Failed to load duas: ${e.toString()}'));
    }
  }

  /// View individual dua
  Future<void> viewDua(DuaModel dua, {DuaCategoryModel? fromCategory}) async {
    try {
      // Store the category info for navigation
      _currentCategory = fromCategory;

      // Get progress for this dua
      final progress = await DuasDatabaseService.getDuaProgress(dua.id);

      emit(DuasViewing(dua: dua, progress: progress));

      LoggerService.duas('🤲 DUAS: Viewing dua: ${dua.title}');
    } catch (e) {
      LoggerService.error('❌ DUAS: Error viewing dua: $e');
      emit(DuasError('Failed to load dua: ${e.toString()}'));
    }
  }

  /// Toggle favorite status
  Future<void> toggleFavorite(DuaModel dua) async {
    try {
      final newFavoriteStatus = !dua.isFavorite;
      await DuasDatabaseService.toggleFavorite(dua.id, newFavoriteStatus);

      // Update the dua object
      final updatedDua = dua.copyWith(isFavorite: newFavoriteStatus);

      // Update current state
      final currentState = state;
      if (currentState is DuasViewing) {
        emit(currentState.copyWith(dua: updatedDua));
      } else if (currentState is DuasCategoryDuasLoaded) {
        final updatedDuas =
            currentState.duas.map((d) {
              return d.id == dua.id ? updatedDua : d;
            }).toList();
        emit(currentState.copyWith(duas: updatedDuas));
      }

      // Refresh favorites list
      _favoriteDuas = await DuasDatabaseService.getFavoriteDuas();

      LoggerService.duas('🤲 DUAS: Toggled favorite for ${dua.title}');
    } catch (e) {
      LoggerService.error('❌ DUAS: Error toggling favorite: $e');
    }
  }

  /// Search duas
  Future<void> searchDuas(String query) async {
    try {
      if (query.trim().isEmpty) {
        await loadCategories();
        return;
      }

      emit(const DuasLoading(progress: 0.5, message: 'Searching...'));

      final results = await DuasDatabaseService.searchDuasAdvanced(query);

      emit(DuasSearchResults(query: query, results: results));

      LoggerService.duas(
        '🤲 DUAS: Found ${results.length} results for "$query"',
      );
    } catch (e) {
      LoggerService.error('❌ DUAS: Error searching duas: $e');
      emit(DuasError('Search failed: ${e.toString()}'));
    }
  }

  /// Show favorites
  Future<void> showFavorites() async {
    try {
      emit(const DuasLoading(progress: 0.5, message: 'Loading favorites...'));

      final favorites = await DuasDatabaseService.getFavoriteDuas();

      emit(DuasFavorites(favoriteDuas: favorites));

      LoggerService.duas('🤲 DUAS: Loaded ${favorites.length} favorite duas');
    } catch (e) {
      LoggerService.error('❌ DUAS: Error loading favorites: $e');
      emit(DuasError('Failed to load favorites: ${e.toString()}'));
    }
  }

  /// Show recent duas
  Future<void> showRecent() async {
    try {
      emit(const DuasLoading(progress: 0.5, message: 'Loading recent duas...'));

      final recent = await DuasDatabaseService.getRecentDuas();

      emit(DuasRecent(recentDuas: recent));

      LoggerService.duas('🤲 DUAS: Loaded ${recent.length} recent duas');
    } catch (e) {
      LoggerService.error('❌ DUAS: Error loading recent duas: $e');
      emit(DuasError('Failed to load recent duas: ${e.toString()}'));
    }
  }

  /// Mark dua as recited (update progress)
  Future<void> markDuaRecited(DuaModel dua) async {
    try {
      final existingProgress = await DuasDatabaseService.getDuaProgress(dua.id);

      final now = DateTime.now();
      final newProgress = DuaProgressModel(
        id: existingProgress?.id ?? '${dua.id}_progress',
        duaId: dua.id,
        lastRecited: now,
        recitationCount: (existingProgress?.recitationCount ?? 0) + 1,
        streakDays: _calculateStreakDays(existingProgress?.lastRecited, now),
        isLearned:
            (existingProgress?.recitationCount ?? 0) >=
            10, // Mark as learned after 10 recitations
      );

      await DuasDatabaseService.updateDuaProgress(newProgress);

      // Update current state if viewing this dua
      final currentState = state;
      if (currentState is DuasViewing && currentState.dua.id == dua.id) {
        emit(currentState.copyWith(progress: newProgress));
      }

      LoggerService.duas('🤲 DUAS: Marked ${dua.title} as recited');
    } catch (e) {
      LoggerService.error('❌ DUAS: Error updating progress: $e');
    }
  }

  /// Calculate streak days
  int _calculateStreakDays(DateTime? lastRecited, DateTime current) {
    if (lastRecited == null) return 1;

    final difference = current.difference(lastRecited).inDays;
    if (difference == 1) {
      return 1; // Continue streak
    } else if (difference == 0) {
      return 1; // Same day
    } else {
      return 1; // Reset streak
    }
  }

  /// Go back to categories
  void backToCategories() {
    _currentCategory = null;
    emit(
      DuasCategoriesLoaded(
        categories: _categories,
        favoriteDuas: _favoriteDuas,
        recentDuas: _recentDuas,
      ),
    );
  }

  /// Go back to category duas
  void backToCategoryDuas(DuaCategoryModel category) async {
    try {
      final duas = await DuasDatabaseService.getDuasByCategory(category.id);
      emit(DuasCategoryDuasLoaded(category: category, duas: duas));
    } catch (e) {
      LoggerService.error('❌ DUAS: Error going back to category: $e');
    }
  }

  /// Smart back navigation
  void goBack() async {
    final currentState = state;

    if (currentState is DuasViewing) {
      // If we came from a category, go back to that category
      if (_currentCategory != null) {
        await loadCategoryDuas(_currentCategory!);
      } else {
        // Otherwise go back to main categories
        backToCategories();
      }
    } else if (currentState is DuasCategoryDuasLoaded) {
      // Go back to categories
      backToCategories();
    } else if (currentState is DuasSearchResults ||
        currentState is DuasFavorites) {
      // Go back to categories
      backToCategories();
    } else {
      // Default - go to categories
      backToCategories();
    }
  }
}
