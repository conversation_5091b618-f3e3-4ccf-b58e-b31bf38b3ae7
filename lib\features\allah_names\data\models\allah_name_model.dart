import 'package:noor_islamic_app/features/allah_names/domain/allah_name.dart';

class AllahNameModel {
  final int number;
  final String name;
  final String transliteration;
  final String meaning;
  final String? explanation;
  final String? quranicVerse;
  final String? reflection;
  final String? supplicationBenefit;

  AllahNameModel({
    required this.number,
    required this.name,
    required this.transliteration,
    required this.meaning,
    this.explanation,
    this.quranicVerse,
    this.reflection,
    this.supplicationBenefit,
  });

  factory AllahNameModel.fromJson(Map<String, dynamic> json) {
    return AllahNameModel(
      number: json['number'],
      name: json['name'],
      transliteration: json['transliteration'],
      meaning: json['meaning'],
      explanation: json['explanation'] as String?,
      quranicVerse: json['quranic_verse'] as String?,
      reflection: json['reflection'] as String?,
      supplicationBenefit: json['supplication_benefit'] as String?,
    );
  }

  AllahName toEntity() {
    return AllahName(
      number: number,
      name: name,
      transliteration: transliteration,
      meaning: meaning,
      explanation: explanation,
      quranicVerse: quranicVerse,
      reflection: reflection,
      supplicationBenefit: supplicationBenefit,
    );
  }
}
