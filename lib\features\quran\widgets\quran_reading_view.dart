import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/quran_models.dart';

class QuranReadingView extends StatefulWidget {
  final SurahModel surah;
  final QuranReadingPreferences preferences;
  final List<BookmarkModel> bookmarks;
  final int? currentAyahIndex;
  final Function(int, int, String) onBookmarkTap;
  final Function(int) onAyahTap;

  const QuranReadingView({
    super.key,
    required this.surah,
    required this.preferences,
    required this.bookmarks,
    this.currentAyahIndex,
    required this.onBookmarkTap,
    required this.onAyahTap,
  });

  @override
  State<QuranReadingView> createState() => _QuranReadingViewState();
}

class _QuranReadingViewState extends State<QuranReadingView> {
  late ItemScrollController _scrollController;
  late ItemPositionsListener _positionsListener;

  @override
  void initState() {
    super.initState();
    _scrollController = ItemScrollController();
    _positionsListener = ItemPositionsListener.create();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Surah header with Bismillah
        _buildSurahHeader(),
        
        // Reading content
        Expanded(
          child: ScrollablePositionedList.builder(
            itemScrollController: _scrollController,
            itemPositionsListener: _positionsListener,
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
            itemCount: widget.surah.ayahs.length,
            itemBuilder: (context, index) {
              final ayah = widget.surah.ayahs[index];
              return _buildAyahCard(ayah, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSurahHeader() {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryGreen,
            AppColors.secondaryGold,
          ],
        ),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGreen.withValues(alpha: 0.3),
            blurRadius: 15.r,
            offset: Offset(0, 5.h),
          ),
        ],
      ),
      child: Column(
        children: [
          // Surah name in Arabic
          Text(
            widget.surah.name,
            style: AppTextStyles.headlineLarge.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w700,
              fontSize: 28.sp,
            ),
            textDirection: TextDirection.rtl,
          ),
          
          SizedBox(height: 8.h),
          
          // English name and translation
          Text(
            widget.surah.englishName,
            style: AppTextStyles.titleLarge.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          
          SizedBox(height: 4.h),
          
          Text(
            widget.surah.englishNameTranslation,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
          
          SizedBox(height: 12.h),
          
          // Surah info
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildInfoChip(widget.surah.revelationType),
              SizedBox(width: 12.w),
              _buildInfoChip('${widget.surah.numberOfAyahs} Ayahs'),
            ],
          ),
          
          // Bismillah (except for Surah At-Tawbah)
          if (widget.surah.number != 9) ...[
            SizedBox(height: 16.h),
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Text(
                'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                style: AppTextStyles.titleLarge.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 20.sp,
                ),
                textDirection: TextDirection.rtl,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoChip(String text) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Text(
        text,
        style: AppTextStyles.bodySmall.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.w600,
          fontSize: 11.sp,
        ),
      ),
    );
  }

  Widget _buildAyahCard(AyahModel ayah, int index) {
    final isBookmarked = widget.bookmarks.any(
      (bookmark) => bookmark.surahNumber == widget.surah.number && 
                   bookmark.ayahNumber == ayah.numberInSurah,
    );
    
    final isCurrentAyah = widget.currentAyahIndex == index;

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: isCurrentAyah
            ? AppColors.primaryGreen.withValues(alpha: 0.1)
            : Theme.of(context).brightness == Brightness.dark
                ? Colors.white.withValues(alpha: 0.05)
                : Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: isCurrentAyah
              ? AppColors.primaryGreen.withValues(alpha: 0.3)
              : Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withValues(alpha: 0.1)
                  : Colors.black.withValues(alpha: 0.1),
          width: isCurrentAyah ? 2 : 1,
        ),
        boxShadow: isCurrentAyah
            ? [
                BoxShadow(
                  color: AppColors.primaryGreen.withValues(alpha: 0.2),
                  blurRadius: 10.r,
                  offset: Offset(0, 2.h),
                ),
              ]
            : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 5.r,
                  offset: Offset(0, 2.h),
                ),
              ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => widget.onAyahTap(index),
          borderRadius: BorderRadius.circular(16.r),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Ayah header
                Row(
                  children: [
                    // Ayah number
                    Container(
                      width: 32.w,
                      height: 32.w,
                      decoration: BoxDecoration(
                        color: AppColors.primaryGreen,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          ayah.numberInSurah.toString(),
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 12.sp,
                          ),
                        ),
                      ),
                    ),
                    
                    const Spacer(),
                    
                    // Bookmark button
                    IconButton(
                      onPressed: () => widget.onBookmarkTap(
                        widget.surah.number,
                        ayah.numberInSurah,
                        widget.surah.englishName,
                      ),
                      icon: Icon(
                        isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                        color: isBookmarked 
                            ? AppColors.secondaryGold 
                            : Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withValues(alpha: 0.7)
                                : Colors.black.withValues(alpha: 0.7),
                        size: 20.sp,
                      ),
                    ),
                  ],
                ),
                
                SizedBox(height: 12.h),
                
                // Arabic text (if enabled)
                if (widget.preferences.showArabic) ...[
                  Text(
                    ayah.text,
                    style: AppTextStyles.headlineMedium.copyWith(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.black87,
                      fontWeight: FontWeight.w500,
                      fontSize: widget.preferences.arabicFontSize.sp,
                      height: widget.preferences.lineSpacing,
                    ),
                    textDirection: TextDirection.rtl,
                    textAlign: TextAlign.right,
                  ),
                  
                  SizedBox(height: 12.h),
                ],
                
                // Translation (if enabled and available)
                if (widget.preferences.showTranslation && 
                    ayah.translations.isNotEmpty) ...[
                  Text(
                    ayah.translations.first.text,
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.9)
                          : Colors.black.withValues(alpha: 0.8),
                      fontSize: widget.preferences.translationFontSize.sp,
                      height: widget.preferences.lineSpacing,
                    ),
                  ),
                ],
                
                // Ayah metadata
                if (ayah.sajda) ...[
                  SizedBox(height: 8.h),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: AppColors.secondaryGold.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      'Sajda',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.secondaryGold,
                        fontWeight: FontWeight.w600,
                        fontSize: 10.sp,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
