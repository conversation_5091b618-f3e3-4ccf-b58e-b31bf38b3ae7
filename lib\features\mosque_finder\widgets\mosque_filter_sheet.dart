import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/mosque_model.dart';

class MosqueFilterSheet extends StatefulWidget {
  final MosqueSearchFilters currentFilters;
  final Function(MosqueSearchFilters) onFiltersChanged;

  const MosqueFilterSheet({
    super.key,
    required this.currentFilters,
    required this.onFiltersChanged,
  });

  @override
  State<MosqueFilterSheet> createState() => _MosqueFilterSheetState();
}

class _MosqueFilterSheetState extends State<MosqueFilterSheet> {
  late double _maxDistance;
  late List<String> _selectedFacilities;
  late String? _selectedDenomination;
  late bool _openNow;

  final List<String> _availableFacilities = [
    'Wheelchair Accessible',
    'Parking Available',
    'Women\'s Section',
    'Wudu Facilities',
    'Air Conditioning',
    'Heating',
  ];

  final List<String> _denominations = [
    'Sunni',
    'Shia',
    'Ahmadiyya',
  ];

  @override
  void initState() {
    super.initState();
    _maxDistance = widget.currentFilters.maxDistance;
    _selectedFacilities = List.from(widget.currentFilters.requiredFacilities);
    _selectedDenomination = widget.currentFilters.denomination;
    _openNow = widget.currentFilters.openNow;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey[900]
            : Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: EdgeInsets.only(top: 8.h),
            width: 40.w,
            height: 4.h,
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withValues(alpha: 0.3)
                  : Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.all(20.w),
            child: Row(
              children: [
                Text(
                  'Filter Mosques',
                  style: AppTextStyles.titleLarge.copyWith(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _resetFilters,
                  child: Text(
                    'Reset',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.primaryGreen,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Filters content
          Flexible(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Distance filter
                  _buildSectionTitle('Distance'),
                  SizedBox(height: 8.h),
                  _buildDistanceSlider(),

                  SizedBox(height: 24.h),

                  // Facilities filter
                  _buildSectionTitle('Facilities'),
                  SizedBox(height: 8.h),
                  _buildFacilitiesFilter(),

                  SizedBox(height: 24.h),

                  // Denomination filter
                  _buildSectionTitle('Denomination'),
                  SizedBox(height: 8.h),
                  _buildDenominationFilter(),

                  SizedBox(height: 24.h),

                  // Open now filter
                  _buildOpenNowFilter(),

                  SizedBox(height: 32.h),
                ],
              ),
            ),
          ),

          // Apply button
          Padding(
            padding: EdgeInsets.all(20.w),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _applyFilters,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryGreen,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                child: Text(
                  'Apply Filters',
                  style: AppTextStyles.titleMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: AppTextStyles.titleMedium.copyWith(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.white
            : Colors.black87,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildDistanceSlider() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Within ${_maxDistance.toStringAsFixed(0)} km',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white.withValues(alpha: 0.8)
                    : Colors.black.withValues(alpha: 0.7),
              ),
            ),
            Text(
              '${_maxDistance.toStringAsFixed(0)} km',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primaryGreen,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: AppColors.primaryGreen,
            inactiveTrackColor: AppColors.primaryGreen.withValues(alpha: 0.3),
            thumbColor: AppColors.primaryGreen,
            overlayColor: AppColors.primaryGreen.withValues(alpha: 0.2),
            trackHeight: 4.h,
          ),
          child: Slider(
            value: _maxDistance,
            min: 1.0,
            max: 50.0,
            divisions: 49,
            onChanged: (value) {
              setState(() {
                _maxDistance = value;
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFacilitiesFilter() {
    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      children: _availableFacilities.map((facility) {
        final isSelected = _selectedFacilities.contains(facility);
        return FilterChip(
          label: Text(
            facility,
            style: AppTextStyles.bodySmall.copyWith(
              color: isSelected
                  ? Colors.white
                  : Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black87,
              fontWeight: FontWeight.w500,
            ),
          ),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              if (selected) {
                _selectedFacilities.add(facility);
              } else {
                _selectedFacilities.remove(facility);
              }
            });
          },
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? Colors.white.withValues(alpha: 0.1)
              : Colors.black.withValues(alpha: 0.05),
          selectedColor: AppColors.primaryGreen,
          checkmarkColor: Colors.white,
          side: BorderSide(
            color: isSelected
                ? AppColors.primaryGreen
                : Theme.of(context).brightness == Brightness.dark
                    ? Colors.white.withValues(alpha: 0.3)
                    : Colors.black.withValues(alpha: 0.2),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDenominationFilter() {
    return Column(
      children: [
        // All denominations option
        RadioListTile<String?>(
          title: Text(
            'All Denominations',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black87,
            ),
          ),
          value: null,
          groupValue: _selectedDenomination,
          onChanged: (value) {
            setState(() {
              _selectedDenomination = value;
            });
          },
          activeColor: AppColors.primaryGreen,
          contentPadding: EdgeInsets.zero,
        ),
        
        // Specific denominations
        ..._denominations.map((denomination) {
          return RadioListTile<String>(
            title: Text(
              denomination,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black87,
              ),
            ),
            value: denomination,
            groupValue: _selectedDenomination,
            onChanged: (value) {
              setState(() {
                _selectedDenomination = value;
              });
            },
            activeColor: AppColors.primaryGreen,
            contentPadding: EdgeInsets.zero,
          );
        }),
      ],
    );
  }

  Widget _buildOpenNowFilter() {
    return SwitchListTile(
      title: Text(
        'Open Now',
        style: AppTextStyles.bodyMedium.copyWith(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white
              : Colors.black87,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        'Show only mosques that are currently open',
        style: AppTextStyles.bodySmall.copyWith(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white.withValues(alpha: 0.7)
              : Colors.black.withValues(alpha: 0.6),
        ),
      ),
      value: _openNow,
      onChanged: (value) {
        setState(() {
          _openNow = value;
        });
      },
      activeColor: AppColors.primaryGreen,
      contentPadding: EdgeInsets.zero,
    );
  }

  void _resetFilters() {
    setState(() {
      _maxDistance = 10.0;
      _selectedFacilities.clear();
      _selectedDenomination = null;
      _openNow = false;
    });
  }

  void _applyFilters() {
    final filters = MosqueSearchFilters(
      maxDistance: _maxDistance,
      requiredFacilities: _selectedFacilities,
      denomination: _selectedDenomination,
      openNow: _openNow,
    );
    
    widget.onFiltersChanged(filters);
    Navigator.pop(context);
  }
}
