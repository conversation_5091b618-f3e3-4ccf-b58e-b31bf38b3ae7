import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../../../core/services/logger_service.dart';
import '../models/dua_models.dart';

class DuasDatabaseService {
  static Database? _database;
  static const String _dbName = 'duas_database.db';
  static const int _dbVersion = 1;

  // Table names
  static const String _categoriesTable = 'dua_categories';
  static const String _duasTable = 'duas';
  static const String _progressTable = 'dua_progress';

  /// Get database instance
  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// Initialize database
  static Future<Database> _initDatabase() async {
    try {
      final dbPath = await getDatabasesPath();
      final path = join(dbPath, _dbName);

      return await openDatabase(
        path,
        version: _dbVersion,
        onCreate: _createTables,
        onUpgrade: _onUpgrade,
      );
    } catch (e) {
      LoggerService.error('❌ DUAS: Error initializing database: $e');
      rethrow;
    }
  }

  /// Create database tables
  static Future<void> _createTables(Database db, int version) async {
    try {
      // Categories table
      await db.execute('''
        CREATE TABLE $_categoriesTable (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT NOT NULL,
          icon_path TEXT NOT NULL,
          color_hex TEXT NOT NULL,
          duas_count INTEGER DEFAULT 0,
          order_index INTEGER DEFAULT 0,
          is_active INTEGER DEFAULT 1,
          created_at TEXT NOT NULL
        )
      ''');

      // Duas table
      await db.execute('''
        CREATE TABLE $_duasTable (
          id TEXT PRIMARY KEY,
          category_id TEXT NOT NULL,
          title TEXT NOT NULL,
          arabic_text TEXT NOT NULL,
          transliteration TEXT NOT NULL,
          english_translation TEXT NOT NULL,
          simple_explanation TEXT NOT NULL,
          benefits TEXT NOT NULL,
          when_to_recite TEXT NOT NULL,
          real_life_examples TEXT NOT NULL,
          hadith_reference TEXT NOT NULL,
          audio_url TEXT DEFAULT '',
          order_index INTEGER DEFAULT 0,
          is_favorite INTEGER DEFAULT 0,
          created_at TEXT NOT NULL,
          FOREIGN KEY (category_id) REFERENCES $_categoriesTable (id)
        )
      ''');

      // Progress tracking table
      await db.execute('''
        CREATE TABLE $_progressTable (
          id TEXT PRIMARY KEY,
          dua_id TEXT NOT NULL,
          last_recited TEXT NOT NULL,
          recitation_count INTEGER DEFAULT 0,
          streak_days INTEGER DEFAULT 0,
          is_learned INTEGER DEFAULT 0,
          created_at TEXT NOT NULL,
          FOREIGN KEY (dua_id) REFERENCES $_duasTable (id)
        )
      ''');

      // Create indexes for better performance
      await db.execute(
        'CREATE INDEX idx_duas_category ON $_duasTable (category_id)',
      );
      await db.execute(
        'CREATE INDEX idx_duas_favorite ON $_duasTable (is_favorite)',
      );
      await db.execute(
        'CREATE INDEX idx_progress_dua ON $_progressTable (dua_id)',
      );

      LoggerService.duas('📖 DUAS: Database tables created successfully');
    } catch (e) {
      LoggerService.error('❌ DUAS: Error creating tables: $e');
      rethrow;
    }
  }

  /// Handle database upgrades
  static Future<void> _onUpgrade(
    Database db,
    int oldVersion,
    int newVersion,
  ) async {
    try {
      LoggerService.duas(
        '📖 DUAS: Upgrading database from $oldVersion to $newVersion',
      );
      // Handle future database upgrades here
    } catch (e) {
      LoggerService.error('❌ DUAS: Error upgrading database: $e');
    }
  }

  /// Insert or update category
  static Future<void> insertCategory(DuaCategoryModel category) async {
    try {
      final db = await database;
      await db.insert(
        _categoriesTable,
        category.toJson()..['created_at'] = DateTime.now().toIso8601String(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      LoggerService.duas('📖 DUAS: Inserted category ${category.name}');
    } catch (e) {
      LoggerService.error('❌ DUAS: Error inserting category: $e');
    }
  }

  /// Get all categories
  static Future<List<DuaCategoryModel>> getCategories() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        _categoriesTable,
        where: 'is_active = ?',
        whereArgs: [1],
        orderBy: 'order_index ASC',
      );

      return maps.map((map) => DuaCategoryModel.fromJson(map)).toList();
    } catch (e) {
      LoggerService.error('❌ DUAS: Error getting categories: $e');
      return [];
    }
  }

  /// Insert or update dua
  static Future<void> insertDua(DuaModel dua) async {
    try {
      final db = await database;
      await db.insert(
        _duasTable,
        dua.toJson(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      LoggerService.duas('📖 DUAS: ✅ Inserted dua ${dua.id}: ${dua.title}');
    } catch (e) {
      LoggerService.error('❌ DUAS: Error inserting dua ${dua.id}: $e');
      LoggerService.error('❌ DUAS: Dua data: ${dua.toJson()}');
      rethrow; // Re-throw to stop the process if there's an error
    }
  }

  /// Get duas by category
  static Future<List<DuaModel>> getDuasByCategory(String categoryId) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        _duasTable,
        where: 'category_id = ?',
        whereArgs: [categoryId],
        orderBy: 'order_index ASC',
      );

      return maps.map((map) => DuaModel.fromJson(map)).toList();
    } catch (e) {
      LoggerService.error('❌ DUAS: Error getting duas by category: $e');
      return [];
    }
  }

  /// Get favorite duas
  static Future<List<DuaModel>> getFavoriteDuas() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        _duasTable,
        where: 'is_favorite = ?',
        whereArgs: [1],
        orderBy: 'title ASC',
      );

      return maps.map((map) => DuaModel.fromJson(map)).toList();
    } catch (e) {
      LoggerService.error('❌ DUAS: Error getting favorite duas: $e');
      return [];
    }
  }

  /// Get recent duas (based on progress/recitation history)
  static Future<List<DuaModel>> getRecentDuas() async {
    try {
      final db = await database;

      // Get duas that have been recited recently (have progress records)
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT d.* FROM $_duasTable d
        INNER JOIN $_progressTable p ON d.id = p.dua_id
        ORDER BY p.last_recited DESC
        LIMIT 10
      ''');

      // If no recent duas from progress, get first few from each category
      if (maps.isEmpty) {
        final List<Map<String, dynamic>> fallbackMaps = await db.rawQuery('''
          SELECT d.* FROM $_duasTable d
          WHERE d.id IN (
            SELECT MIN(id) FROM $_duasTable
            GROUP BY category_id
          )
          ORDER BY d.order_index ASC
          LIMIT 6
        ''');
        return fallbackMaps.map((map) => DuaModel.fromJson(map)).toList();
      }

      return maps.map((map) => DuaModel.fromJson(map)).toList();
    } catch (e) {
      LoggerService.error('❌ DUAS: Error getting recent duas: $e');
      return [];
    }
  }

  /// Toggle favorite status
  static Future<void> toggleFavorite(String duaId, bool isFavorite) async {
    try {
      final db = await database;
      await db.update(
        _duasTable,
        {'is_favorite': isFavorite ? 1 : 0},
        where: 'id = ?',
        whereArgs: [duaId],
      );
      LoggerService.duas('📖 DUAS: Toggled favorite for dua $duaId');
    } catch (e) {
      LoggerService.error('❌ DUAS: Error toggling favorite: $e');
    }
  }

  /// Advanced search duas with intelligent ranking
  static Future<List<DuaModel>> searchDuasAdvanced(String query) async {
    try {
      final db = await database;
      final cleanQuery = query.toLowerCase().trim();

      if (cleanQuery.isEmpty) return [];

      // Create search terms
      final exactMatch = cleanQuery;
      final startsWithMatch = '$cleanQuery%';
      final containsMatch = '%$cleanQuery%';

      final List<Map<String, dynamic>> maps = await db.rawQuery(
        '''
        SELECT *,
          -- Relevance scoring system
          (CASE
            -- Exact title match (highest priority)
            WHEN LOWER(title) = ? THEN 100
            -- Title starts with query
            WHEN LOWER(title) LIKE ? THEN 95
            -- Title contains query
            WHEN LOWER(title) LIKE ? THEN 90

            -- Exact translation match
            WHEN LOWER(english_translation) = ? THEN 85
            -- Translation starts with query
            WHEN LOWER(english_translation) LIKE ? THEN 80
            -- Translation contains query
            WHEN LOWER(english_translation) LIKE ? THEN 75

            -- Explanation starts with query
            WHEN LOWER(simple_explanation) LIKE ? THEN 70
            -- Explanation contains query
            WHEN LOWER(simple_explanation) LIKE ? THEN 65

            -- Benefits contain query
            WHEN LOWER(benefits) LIKE ? THEN 60

            -- When to recite contains query
            WHEN LOWER(when_to_recite) LIKE ? THEN 55

            -- Transliteration contains query (for Arabic searches)
            WHEN LOWER(transliteration) LIKE ? THEN 50

            -- Real life examples contain query
            WHEN LOWER(real_life_examples) LIKE ? THEN 45

            -- Hadith reference contains query
            WHEN LOWER(hadith_reference) LIKE ? THEN 40

            -- Arabic text contains query (for Arabic searches)
            WHEN LOWER(arabic_text) LIKE ? THEN 35

            ELSE 30
          END) as relevance_score
        FROM $_duasTable
        WHERE
          LOWER(title) LIKE ? OR
          LOWER(english_translation) LIKE ? OR
          LOWER(simple_explanation) LIKE ? OR
          LOWER(benefits) LIKE ? OR
          LOWER(when_to_recite) LIKE ? OR
          LOWER(real_life_examples) LIKE ? OR
          LOWER(hadith_reference) LIKE ? OR
          LOWER(transliteration) LIKE ? OR
          LOWER(arabic_text) LIKE ?
        ORDER BY relevance_score DESC, order_index ASC
        LIMIT 50
      ''',
        [
          // Relevance scoring parameters
          exactMatch, startsWithMatch, containsMatch, // title
          exactMatch, startsWithMatch, containsMatch, // translation
          startsWithMatch, containsMatch, // explanation
          containsMatch, // benefits
          containsMatch, // when_to_recite
          containsMatch, // transliteration
          containsMatch, // real_life_examples
          containsMatch, // hadith_reference
          containsMatch, // arabic_text
          // WHERE clause parameters
          containsMatch, containsMatch, containsMatch, containsMatch,
          containsMatch,
          containsMatch,
          containsMatch,
          containsMatch,
          containsMatch,
        ],
      );

      final results = maps.map((map) => DuaModel.fromJson(map)).toList();

      LoggerService.duas(
        '🔍 DUAS: Advanced search for "$query" found ${results.length} results',
      );

      return results;
    } catch (e) {
      LoggerService.error('❌ DUAS: Error in advanced search: $e');
      return [];
    }
  }

  /// Legacy search method (keeping for compatibility)
  static Future<List<DuaModel>> searchDuas(String query) async {
    return searchDuasAdvanced(query);
  }

  /// Update dua progress
  static Future<void> updateDuaProgress(DuaProgressModel progress) async {
    try {
      final db = await database;
      await db.insert(
        _progressTable,
        progress.toJson()..['created_at'] = DateTime.now().toIso8601String(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      LoggerService.duas('📖 DUAS: Updated progress for dua ${progress.duaId}');
    } catch (e) {
      LoggerService.error('❌ DUAS: Error updating progress: $e');
    }
  }

  /// Get dua progress
  static Future<DuaProgressModel?> getDuaProgress(String duaId) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        _progressTable,
        where: 'dua_id = ?',
        whereArgs: [duaId],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return DuaProgressModel.fromJson(maps.first);
      }
      return null;
    } catch (e) {
      LoggerService.error('❌ DUAS: Error getting progress: $e');
      return null;
    }
  }

  /// Clear all data (for testing)
  static Future<void> clearAllData() async {
    try {
      final db = await database;
      await db.delete(_progressTable);
      await db.delete(_duasTable);
      await db.delete(_categoriesTable);
      LoggerService.duas('📖 DUAS: Cleared all data');
    } catch (e) {
      LoggerService.error('❌ DUAS: Error clearing data: $e');
    }
  }

  /// Get total count of duas in database (for debugging)
  static Future<int> getTotalDuasCount() async {
    try {
      final db = await database;
      final result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM $_duasTable',
      );
      final count = result.first['count'] as int;
      LoggerService.duas('📖 DUAS: Total duas in database: $count');
      return count;
    } catch (e) {
      LoggerService.error('❌ DUAS: Error getting total count: $e');
      return 0;
    }
  }
}
