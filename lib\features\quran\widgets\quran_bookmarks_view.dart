import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/quran_models.dart';
import '../cubit/quran_cubit.dart';
import '../cubit/quran_state.dart';

class QuranBookmarksView extends StatelessWidget {
  const QuranBookmarksView({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          // Go back to Surah list, not home
          context.read<QuranCubit>().backToSurahs();
        }
      },
      child: BlocBuilder<QuranCubit, QuranState>(
        builder: (context, state) {
          if (state is! QuranBookmarks) {
            return const SizedBox.shrink();
          }

          final bookmarks = state.bookmarks;
          final preferences = state.preferences;

          return Column(
            children: [
              // Header
              _buildHeader(context, bookmarks),

              // Content
              Expanded(
                child:
                    bookmarks.isEmpty
                        ? _buildEmptyState(context)
                        : _buildBookmarksList(context, bookmarks, preferences),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildHeader(BuildContext context, List<BookmarkModel> bookmarks) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryGreen.withValues(alpha: 0.1),
            AppColors.secondaryGold.withValues(alpha: 0.05),
          ],
        ),
        border: Border(
          bottom: BorderSide(
            color: AppColors.primaryGreen.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => context.read<QuranCubit>().backToSurahs(),
            icon: Icon(
              Icons.arrow_back,
              color: AppColors.primaryGreen,
              size: 24.sp,
            ),
          ),
          SizedBox(width: 8.w),
          Icon(Icons.bookmark, color: AppColors.primaryGreen, size: 24.sp),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Bookmarked Surahs',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: AppColors.primaryGreen,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                Text(
                  '${bookmarks.length} ${bookmarks.length == 1 ? 'Surah' : 'Surahs'} saved',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.primaryGreen.withValues(alpha: 0.8),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          if (bookmarks.isNotEmpty)
            TextButton.icon(
              onPressed: () => _showClearAllDialog(context, bookmarks),
              icon: Icon(
                Icons.clear_all,
                size: 18.sp,
                color: AppColors.primaryGreen,
              ),
              label: Text(
                'Clear All',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.primaryGreen,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBookmarksList(
    BuildContext context,
    List<BookmarkModel> bookmarks,
    QuranReadingPreferences preferences,
  ) {
    return ListView.builder(
      padding: EdgeInsets.all(20.w),
      itemCount: bookmarks.length,
      itemBuilder: (context, index) {
        final bookmark = bookmarks[index];
        return _buildBookmarkCard(context, bookmark);
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_border,
            size: 64.sp,
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? Colors.white.withValues(alpha: 0.5)
                    : Colors.black.withValues(alpha: 0.5),
          ),

          SizedBox(height: 16.h),

          Text(
            'No Bookmarks Yet',
            style: AppTextStyles.titleLarge.copyWith(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black87,
            ),
          ),

          SizedBox(height: 8.h),

          Text(
            'Bookmark verses while reading to save them here',
            style: AppTextStyles.bodyMedium.copyWith(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.7)
                      : Colors.black.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBookmarkCard(BuildContext context, BookmarkModel bookmark) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? Colors.white.withValues(alpha: 0.1)
                : Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withValues(alpha: 0.2)
                  : Colors.black.withValues(alpha: 0.1),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap:
              () => context.read<QuranCubit>().openSurah(bookmark.surahNumber),
          borderRadius: BorderRadius.circular(12.r),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                // Bookmark icon
                Container(
                  width: 40.w,
                  height: 40.w,
                  decoration: BoxDecoration(
                    color: AppColors.secondaryGold.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    Icons.bookmark,
                    color: AppColors.secondaryGold,
                    size: 20.sp,
                  ),
                ),

                SizedBox(width: 12.w),

                // Bookmark info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${bookmark.surahName} - Ayah ${bookmark.ayahNumber}',
                        style: AppTextStyles.titleMedium.copyWith(
                          color:
                              Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white
                                  : Colors.black87,
                          fontWeight: FontWeight.w600,
                        ),
                      ),

                      if (bookmark.note.isNotEmpty) ...[
                        SizedBox(height: 4.h),
                        Text(
                          bookmark.note,
                          style: AppTextStyles.bodySmall.copyWith(
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? Colors.white.withValues(alpha: 0.7)
                                    : Colors.black.withValues(alpha: 0.6),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],

                      SizedBox(height: 4.h),

                      Text(
                        _formatDate(bookmark.createdAt),
                        style: AppTextStyles.bodySmall.copyWith(
                          color:
                              Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white.withValues(alpha: 0.5)
                                  : Colors.black.withValues(alpha: 0.5),
                        ),
                      ),
                    ],
                  ),
                ),

                // Delete button
                IconButton(
                  onPressed:
                      () => context.read<QuranCubit>().removeBookmark(
                        bookmark.id,
                      ),
                  icon: Icon(
                    Icons.delete_outline,
                    color:
                        Theme.of(context).brightness == Brightness.dark
                            ? Colors.white.withValues(alpha: 0.7)
                            : Colors.black.withValues(alpha: 0.7),
                    size: 20.sp,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

  void _showClearAllDialog(
    BuildContext context,
    List<BookmarkModel> bookmarks,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Clear All Bookmarks',
            style: AppTextStyles.titleLarge.copyWith(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black87,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            'Are you sure you want to remove all ${bookmarks.length} bookmarked Surahs? This action cannot be undone.',
            style: AppTextStyles.bodyMedium.copyWith(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.8)
                      : Colors.black.withValues(alpha: 0.7),
            ),
          ),
          backgroundColor:
              Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey[900]
                  : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.r),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: AppTextStyles.titleMedium.copyWith(
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.7)
                          : Colors.black.withValues(alpha: 0.6),
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Use the safe clear all method
                context.read<QuranCubit>().clearAllBookmarks();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: Text(
                'Clear All',
                style: AppTextStyles.titleMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
