import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

class MosqueSearchBar extends StatefulWidget {
  final Function(String) onSearch;
  final VoidCallback onLocationTap;
  final String currentLocation;

  const MosqueSearchBar({
    super.key,
    required this.onSearch,
    required this.onLocationTap,
    required this.currentLocation,
  });

  @override
  State<MosqueSearchBar> createState() => _MosqueSearchBarState();
}

class _MosqueSearchBarState extends State<MosqueSearchBar>
    with TickerProviderStateMixin {
  late TextEditingController _searchController;
  late AnimationController _locationController;
  late Animation<double> _locationAnimation;
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    
    _locationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _locationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _locationController, curve: Curves.easeInOut),
    );
    
    _locationController.forward();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Location indicator
        FadeTransition(
          opacity: _locationAnimation,
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withValues(alpha: 0.1)
                  : Colors.black.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white.withValues(alpha: 0.2)
                    : Colors.black.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: InkWell(
              onTap: widget.onLocationTap,
              borderRadius: BorderRadius.circular(12.r),
              child: Row(
                children: [
                  Icon(
                    Icons.location_on,
                    color: AppColors.primaryGreen,
                    size: 20.sp,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      widget.currentLocation,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black87,
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.7)
                        : Colors.black.withValues(alpha: 0.7),
                    size: 20.sp,
                  ),
                ],
              ),
            ),
          ),
        ),
        
        SizedBox(height: 12.h),
        
        // Search bar
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white.withValues(alpha: 0.1)
                : Colors.black.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(
              color: _isSearching
                  ? AppColors.primaryGreen
                  : Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.2)
                      : Colors.black.withValues(alpha: 0.1),
              width: _isSearching ? 2 : 1,
            ),
            boxShadow: _isSearching
                ? [
                    BoxShadow(
                      color: AppColors.primaryGreen.withValues(alpha: 0.2),
                      blurRadius: 8.r,
                      spreadRadius: 0,
                    ),
                  ]
                : null,
          ),
          child: TextField(
            controller: _searchController,
            onChanged: (value) {
              widget.onSearch(value);
              setState(() {
                _isSearching = value.isNotEmpty;
              });
            },
            onTap: () {
              setState(() {
                _isSearching = true;
              });
            },
            onSubmitted: (value) {
              setState(() {
                _isSearching = false;
              });
            },
            style: AppTextStyles.bodyMedium.copyWith(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black87,
            ),
            decoration: InputDecoration(
              hintText: 'Search mosques by name or area...',
              hintStyle: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white.withValues(alpha: 0.6)
                    : Colors.black.withValues(alpha: 0.5),
              ),
              prefixIcon: Icon(
                Icons.search,
                color: _isSearching
                    ? AppColors.primaryGreen
                    : Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.7)
                        : Colors.black.withValues(alpha: 0.7),
                size: 20.sp,
              ),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        widget.onSearch('');
                        setState(() {
                          _isSearching = false;
                        });
                      },
                      icon: Icon(
                        Icons.clear,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white.withValues(alpha: 0.7)
                            : Colors.black.withValues(alpha: 0.7),
                        size: 20.sp,
                      ),
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 16.h,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
