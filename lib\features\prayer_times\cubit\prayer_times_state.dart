part of 'prayer_times_cubit.dart';

abstract class PrayerTimesState extends Equatable {
  const PrayerTimesState();

  @override
  List<Object?> get props => [];
}

class PrayerTimesInitial extends PrayerTimesState {}

class PrayerTimesLoading extends PrayerTimesState {}

class PrayerTimesLoaded extends PrayerTimesState {
  final PrayerTimesModel prayerTimes;
  final PrayerInfo nextPrayer;
  final PrayerInfo currentPrayer;

  const PrayerTimesLoaded({
    required this.prayerTimes,
    required this.nextPrayer,
    required this.currentPrayer,
  });

  @override
  List<Object?> get props => [prayerTimes, nextPrayer, currentPrayer];
}

class PrayerTimesLoadedWithWeekly extends PrayerTimesLoaded {
  final List<PrayerTimesModel> weeklyTimes;

  const PrayerTimesLoadedWithWeekly({
    required super.prayerTimes,
    required super.nextPrayer,
    required super.currentPrayer,
    required this.weeklyTimes,
  });

  @override
  List<Object?> get props => [prayerTimes, nextPrayer, currentPrayer, weeklyTimes];
}

class PrayerTimesError extends PrayerTimesState {
  final String message;

  const PrayerTimesError({required this.message});

  @override
  List<Object?> get props => [message];
}
