import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../../../core/services/logger_service.dart';
import '../models/quran_models.dart';

/// Database service for offline Quran storage and caching
class QuranDatabaseService {
  static Database? _database;
  static const String _databaseName = 'quran_database.db';
  static const int _databaseVersion = 1;

  /// Initialize database
  static Future<void> initializeDatabase() async {
    try {
      LoggerService.quran('📖 QURAN: Initializing database...');

      final databasePath = await getDatabasesPath();
      final path = join(databasePath, _databaseName);

      _database = await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _createDatabase,
        onUpgrade: _upgradeDatabase,
      );

      LoggerService.quran('📖 QURAN: Database initialized successfully');
    } catch (e) {
      LoggerService.error('❌ QURAN: Database initialization error: $e');
      throw Exception('Failed to initialize Quran database: ${e.toString()}');
    }
  }

  /// Create database tables
  static Future<void> _createDatabase(Database db, int version) async {
    // Surahs table
    await db.execute('''
      CREATE TABLE surahs (
        number INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        english_name TEXT NOT NULL,
        english_name_translation TEXT NOT NULL,
        revelation_type TEXT NOT NULL,
        number_of_ayahs INTEGER NOT NULL,
        cached_at TEXT NOT NULL
      )
    ''');

    // Ayahs table
    await db.execute('''
      CREATE TABLE ayahs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        number INTEGER NOT NULL,
        text TEXT NOT NULL,
        number_in_surah INTEGER NOT NULL,
        surah_number INTEGER NOT NULL,
        juz INTEGER NOT NULL,
        manzil INTEGER NOT NULL,
        page INTEGER NOT NULL,
        ruku INTEGER NOT NULL,
        hizb_quarter INTEGER NOT NULL,
        sajda INTEGER NOT NULL DEFAULT 0,
        translation_text TEXT,
        translation_language TEXT,
        cached_at TEXT NOT NULL,
        FOREIGN KEY (surah_number) REFERENCES surahs (number)
      )
    ''');

    // Bookmarks table
    await db.execute('''
      CREATE TABLE bookmarks (
        id TEXT PRIMARY KEY,
        surah_number INTEGER NOT NULL,
        ayah_number INTEGER NOT NULL,
        surah_name TEXT NOT NULL,
        note TEXT,
        created_at TEXT NOT NULL
      )
    ''');

    // Preferences table
    await db.execute('''
      CREATE TABLE preferences (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      )
    ''');

    LoggerService.quran('📖 QURAN: Database tables created');
  }

  /// Upgrade database
  static Future<void> _upgradeDatabase(
    Database db,
    int oldVersion,
    int newVersion,
  ) async {
    // Handle database upgrades here
    LoggerService.quran(
      '📖 QURAN: Database upgraded from $oldVersion to $newVersion',
    );
  }

  /// Get database instance
  static Future<Database> get database async {
    if (_database != null) return _database!;
    await initializeDatabase();
    return _database!;
  }

  /// Cache Surahs list
  static Future<void> cacheSurahs(List<SurahModel> surahs) async {
    try {
      final db = await database;
      final batch = db.batch();

      // Clear existing Surahs
      batch.delete('surahs');

      // Insert new Surahs
      for (final surah in surahs) {
        batch.insert('surahs', {
          'number': surah.number,
          'name': surah.name,
          'english_name': surah.englishName,
          'english_name_translation': surah.englishNameTranslation,
          'revelation_type': surah.revelationType,
          'number_of_ayahs': surah.numberOfAyahs,
          'cached_at': DateTime.now().toIso8601String(),
        });
      }

      await batch.commit();
      LoggerService.quran('📖 QURAN: Cached ${surahs.length} Surahs');
    } catch (e) {
      LoggerService.error('❌ QURAN: Error caching Surahs: $e');
    }
  }

  /// Get cached Surahs
  static Future<List<SurahModel>> getCachedSurahs() async {
    try {
      final db = await database;
      final maps = await db.query('surahs', orderBy: 'number ASC');

      return maps
          .map(
            (map) => SurahModel(
              number: map['number'] as int,
              name: map['name'] as String,
              englishName: map['english_name'] as String,
              englishNameTranslation: map['english_name_translation'] as String,
              revelationType: map['revelation_type'] as String,
              numberOfAyahs: map['number_of_ayahs'] as int,
            ),
          )
          .toList();
    } catch (e) {
      LoggerService.error('❌ QURAN: Error getting cached Surahs: $e');
      return [];
    }
  }

  /// Cache Surah with translation
  static Future<void> cacheSurahWithTranslation(SurahModel surah) async {
    try {
      final db = await database;
      final batch = db.batch();

      // Delete existing Ayahs for this Surah
      batch.delete(
        'ayahs',
        where: 'surah_number = ?',
        whereArgs: [surah.number],
      );

      // Insert Ayahs with translations
      for (final ayah in surah.ayahs) {
        final translationText =
            ayah.translations.isNotEmpty ? ayah.translations.first.text : '';
        final translationLanguage =
            ayah.translations.isNotEmpty
                ? ayah.translations.first.language
                : '';

        batch.insert('ayahs', {
          'number': ayah.number,
          'text': ayah.text,
          'number_in_surah': ayah.numberInSurah,
          'surah_number': surah.number,
          'juz': ayah.juz,
          'manzil': ayah.manzil,
          'page': ayah.page,
          'ruku': ayah.ruku,
          'hizb_quarter': ayah.hizbQuarter,
          'sajda': ayah.sajda ? 1 : 0,
          'translation_text': translationText,
          'translation_language': translationLanguage,
          'cached_at': DateTime.now().toIso8601String(),
        });
      }

      await batch.commit();
      LoggerService.quran(
        '📖 QURAN: Cached Surah ${surah.englishName} with ${surah.ayahs.length} Ayahs',
      );
    } catch (e) {
      LoggerService.error('❌ QURAN: Error caching Surah: $e');
    }
  }

  /// Get cached Surah with translation
  static Future<SurahModel?> getCachedSurahWithTranslation(
    int surahNumber,
  ) async {
    try {
      final db = await database;

      // Get Surah info
      final surahMaps = await db.query(
        'surahs',
        where: 'number = ?',
        whereArgs: [surahNumber],
      );

      if (surahMaps.isEmpty) return null;

      final surahMap = surahMaps.first;

      // Get Ayahs
      final ayahMaps = await db.query(
        'ayahs',
        where: 'surah_number = ?',
        whereArgs: [surahNumber],
        orderBy: 'number_in_surah ASC',
      );

      final ayahs =
          ayahMaps.map((map) {
            final translations = <TranslationModel>[];
            if (map['translation_text'] != null &&
                (map['translation_text'] as String).isNotEmpty) {
              translations.add(
                TranslationModel(
                  id: 1,
                  language: map['translation_language'] as String,
                  text: map['translation_text'] as String,
                  resourceName: 'cached',
                ),
              );
            }

            return AyahModel(
              number: map['number'] as int,
              text: map['text'] as String,
              numberInSurah: map['number_in_surah'] as int,
              juz: map['juz'] as int,
              manzil: map['manzil'] as int,
              page: map['page'] as int,
              ruku: map['ruku'] as int,
              hizbQuarter: map['hizb_quarter'] as int,
              sajda: (map['sajda'] as int) == 1,
              translations: translations,
            );
          }).toList();

      return SurahModel(
        number: surahMap['number'] as int,
        name: surahMap['name'] as String,
        englishName: surahMap['english_name'] as String,
        englishNameTranslation: surahMap['english_name_translation'] as String,
        revelationType: surahMap['revelation_type'] as String,
        numberOfAyahs: surahMap['number_of_ayahs'] as int,
        ayahs: ayahs,
      );
    } catch (e) {
      LoggerService.error('❌ QURAN: Error getting cached Surah: $e');
      return null;
    }
  }

  /// Add bookmark
  static Future<void> addBookmark(BookmarkModel bookmark) async {
    try {
      final db = await database;
      await db.insert('bookmarks', bookmark.toJson());
      LoggerService.quran('📖 QURAN: Added bookmark');
    } catch (e) {
      LoggerService.error('❌ QURAN: Error adding bookmark: $e');
    }
  }

  /// Remove bookmark
  static Future<void> removeBookmark(String bookmarkId) async {
    try {
      final db = await database;
      LoggerService.quran('📖 QURAN: Deleting bookmark with ID: $bookmarkId');

      final result = await db.delete(
        'bookmarks',
        where: 'id = ?',
        whereArgs: [bookmarkId],
      );

      LoggerService.quran(
        '📖 QURAN: Database delete result: $result rows affected',
      );

      if (result > 0) {
        LoggerService.quran(
          '📖 QURAN: Successfully removed bookmark $bookmarkId',
        );
      } else {
        LoggerService.quran(
          '📖 QURAN: Warning: No bookmark found with ID $bookmarkId',
        );
      }
    } catch (e) {
      LoggerService.error('❌ QURAN: Error removing bookmark $bookmarkId: $e');
    }
  }

  /// Get all bookmarks
  static Future<List<BookmarkModel>> getBookmarks() async {
    try {
      final db = await database;
      final maps = await db.query('bookmarks', orderBy: 'created_at DESC');

      return maps.map((map) => BookmarkModel.fromJson(map)).toList();
    } catch (e) {
      LoggerService.error('❌ QURAN: Error getting bookmarks: $e');
      return [];
    }
  }

  /// Save preferences
  static Future<void> savePreferences(
    QuranReadingPreferences preferences,
  ) async {
    try {
      final db = await database;
      final batch = db.batch();

      // Save each preference (no selectedTranslation - using fixed Sahih International)
      batch.insert('preferences', {
        'key': 'showArabic',
        'value': preferences.showArabic.toString(),
      }, conflictAlgorithm: ConflictAlgorithm.replace);
      batch.insert('preferences', {
        'key': 'showTranslation',
        'value': preferences.showTranslation.toString(),
      }, conflictAlgorithm: ConflictAlgorithm.replace);
      batch.insert('preferences', {
        'key': 'showTransliteration',
        'value': preferences.showTransliteration.toString(),
      }, conflictAlgorithm: ConflictAlgorithm.replace);
      batch.insert('preferences', {
        'key': 'arabicFontSize',
        'value': preferences.arabicFontSize.toString(),
      }, conflictAlgorithm: ConflictAlgorithm.replace);
      batch.insert('preferences', {
        'key': 'translationFontSize',
        'value': preferences.translationFontSize.toString(),
      }, conflictAlgorithm: ConflictAlgorithm.replace);
      batch.insert('preferences', {
        'key': 'arabicFont',
        'value': preferences.arabicFont,
      }, conflictAlgorithm: ConflictAlgorithm.replace);
      batch.insert('preferences', {
        'key': 'nightMode',
        'value': preferences.nightMode.toString(),
      }, conflictAlgorithm: ConflictAlgorithm.replace);
      batch.insert('preferences', {
        'key': 'lineSpacing',
        'value': preferences.lineSpacing.toString(),
      }, conflictAlgorithm: ConflictAlgorithm.replace);

      await batch.commit();
      LoggerService.quran('📖 QURAN: Saved preferences');
    } catch (e) {
      LoggerService.error('❌ QURAN: Error saving preferences: $e');
    }
  }

  /// Get preferences
  static Future<QuranReadingPreferences> getPreferences() async {
    try {
      final db = await database;
      final maps = await db.query('preferences');

      final prefs = <String, String>{};
      for (final map in maps) {
        prefs[map['key'] as String] = map['value'] as String;
      }

      return QuranReadingPreferences(
        showArabic: prefs['showArabic'] != 'false', // Default true
        showTranslation:
            prefs['showTranslation'] == 'true', // Default false (Arabic-only)
        showTransliteration: prefs['showTransliteration'] == 'true',
        arabicFontSize:
            double.tryParse(prefs['arabicFontSize'] ?? '22.0') ?? 22.0,
        translationFontSize:
            double.tryParse(prefs['translationFontSize'] ?? '16.0') ?? 16.0,
        arabicFont: prefs['arabicFont'] ?? 'Amiri', // Best Arabic font
        nightMode: prefs['nightMode'] == 'true',
        lineSpacing: double.tryParse(prefs['lineSpacing'] ?? '1.8') ?? 1.8,
      );
    } catch (e) {
      LoggerService.error('❌ QURAN: Error getting preferences: $e');
      return const QuranReadingPreferences();
    }
  }

  /// Clear all cache
  static Future<void> clearCache() async {
    try {
      final db = await database;
      await db.delete('surahs');
      await db.delete('ayahs');
      LoggerService.quran('📖 QURAN: Cache cleared');
    } catch (e) {
      LoggerService.error('❌ QURAN: Error clearing cache: $e');
    }
  }

  /// Close database
  static Future<void> closeDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      LoggerService.quran('📖 QURAN: Database closed');
    }
  }
}
