import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:noor_islamic_app/features/allah_names/data/repository/allah_names_repository_impl.dart';
import 'package:noor_islamic_app/features/allah_names/presentation/bloc/allah_names_bloc.dart';
import 'package:noor_islamic_app/features/allah_names/presentation/widgets/allah_names_view.dart';

class AllahNamesScreen extends StatelessWidget {
  const AllahNamesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AllahNamesBloc(
        AllahNamesRepositoryImpl(),
      )..add(FetchAllah<PERSON>ames()),
      child: const AllahNamesView(),
    );
  }
}
