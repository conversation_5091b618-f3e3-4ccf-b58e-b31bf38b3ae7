import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../domain/allah_name.dart';

class AllahNameDetailSheet extends StatelessWidget {
  final AllahName name;
  const AllahNameDetailSheet({super.key, required this.name});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return DraggableScrollableSheet(
      initialChildSize: 0.6,
      maxChildSize: 0.95,
      minChildSize: 0.4,
      builder: (context, controller) {
        return Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
          ),
          child: <PERSON>umn(
            children: [
              _buildDragHandle(theme),
              _buildHeader(theme),
              const Divider(height: 1, thickness: 1),
              Expanded(
                child: ListView(
                  controller: controller,
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  children: [
                    SizedBox(height: 16.h),
                    if (name.explanation != null &&
                        name.explanation!.isNotEmpty)
                      _buildSection(
                        icon: Icons.info_outline,
                        title: 'Explanation',
                        content: name.explanation!,
                        theme: theme,
                      ),
                    if (name.quranicVerse != null &&
                        name.quranicVerse!.isNotEmpty)
                      _buildSection(
                        icon: Icons.menu_book_outlined,
                        title: 'Quranic Reference',
                        content: name.quranicVerse!,
                        theme: theme,
                      ),
                    if (name.reflection != null && name.reflection!.isNotEmpty)
                      _buildSection(
                        icon: Icons.lightbulb_outline,
                        title: 'Reflection',
                        content: name.reflection!,
                        theme: theme,
                      ),
                    if (name.supplicationBenefit != null &&
                        name.supplicationBenefit!.isNotEmpty)
                      _buildSection(
                        icon: Icons.volunteer_activism_outlined,
                        title: 'Supplication Benefit',
                        content: name.supplicationBenefit!,
                        theme: theme,
                      ),
                    SizedBox(height: 40.h), // Bottom padding
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDragHandle(ThemeData theme) {
    return Center(
      child: Container(
        width: 40.w,
        height: 4.h,
        margin: EdgeInsets.symmetric(vertical: 12.h),
        decoration: BoxDecoration(
          color: theme.colorScheme.outline, // FIXED: Use proper theme color
          borderRadius: BorderRadius.circular(2),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Padding(
      padding: EdgeInsets.fromLTRB(20.w, 0, 20.w, 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            name.name, // Arabic Name
            style: TextStyle(
              fontFamily: 'Amiri',
              fontSize: 48.sp,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            name.transliteration,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            name.meaning,
            textAlign: TextAlign.center,
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.secondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required IconData icon,
    required String title,
    required String content,
    required ThemeData theme,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Theme(
        data: theme.copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          leading: Icon(icon, color: theme.colorScheme.primary),
          tilePadding: EdgeInsets.zero,
          title: Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(16.w, 8.h, 16.w, 8.h),
              child: Text(
                content,
                style: theme.textTheme.bodyLarge?.copyWith(
                  height: 1.6,
                  color:
                      theme
                          .colorScheme
                          .onSurfaceVariant, // FIXED: Use proper theme color
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
