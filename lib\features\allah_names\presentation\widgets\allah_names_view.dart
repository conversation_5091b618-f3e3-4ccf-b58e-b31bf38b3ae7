import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:noor_islamic_app/features/allah_names/presentation/bloc/allah_names_bloc.dart';
import 'package:noor_islamic_app/features/allah_names/presentation/widgets/allah_name_card.dart';
import 'package:noor_islamic_app/features/allah_names/presentation/widgets/allah_name_detail_sheet.dart';
import 'package:noor_islamic_app/presentation/widgets/gradient_background.dart';

class AllahNamesView extends StatelessWidget {
  const AllahNamesView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: const Text('Asma-ul-Husna'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: GradientBackground(
        child: <PERSON><PERSON><PERSON>er<AllahNamesBloc, AllahNamesState>(
          builder: (context, state) {
            if (state is AllahNamesLoading || state is AllahNamesInitial) {
              return const Center(child: CircularProgressIndicator());
            } else if (state is AllahNamesLoaded) {
              return AnimationLimiter(
                child: GridView.builder(
                  padding: EdgeInsets.fromLTRB(16.w, 100.h, 16.w, 16.w),
                  itemCount: state.names.length,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.85,
                    crossAxisSpacing: 16.w,
                    mainAxisSpacing: 16.h,
                  ),
                  itemBuilder: (context, index) {
                    return AnimationConfiguration.staggeredGrid(
                      position: index,
                      duration: const Duration(milliseconds: 375),
                      columnCount: 2,
                      child: ScaleAnimation(
                        duration: const Duration(milliseconds: 500),
                        curve: Curves.easeOutBack,
                        child: FadeInAnimation(
                          duration: const Duration(milliseconds: 500),
                          child: InkWell(
                            borderRadius: BorderRadius.circular(16.r),
                            onTap: () {
                              showModalBottomSheet(
                                context: context,
                                isScrollControlled: true,
                                backgroundColor: Colors.transparent,
                                builder: (_) => AllahNameDetailSheet(
                                  name: state.names[index],
                                ),
                              );
                            },
                            child: AllahNameCard(name: state.names[index]),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              );
            } else if (state is AllahNamesError) {
              return Center(child: Text(state.message));
            } else {
              return const Center(child: Text('Something went wrong!'));
            }
          },
        ),
      ),
    );
  }
}
