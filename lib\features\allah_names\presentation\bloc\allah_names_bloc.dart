import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:noor_islamic_app/features/allah_names/domain/allah_name.dart';
import 'package:noor_islamic_app/features/allah_names/domain/allah_names_repository.dart';

part 'allah_names_event.dart';
part 'allah_names_state.dart';

class AllahNamesBloc extends Bloc<AllahNamesEvent, AllahNamesState> {
  final AllahNamesRepository _allahNamesRepository;

  AllahNamesBloc(this._allahNamesRepository) : super(AllahNamesInitial()) {
    on<FetchAllahNames>((event, emit) async {
      emit(AllahNamesLoading());
      try {
        final names = await _allahNamesRepository.getAllahNames();
        emit(AllahNamesLoaded(names));
      } catch (e) {
        emit(AllahNamesError(e.toString()));
      }
    });
  }
}
