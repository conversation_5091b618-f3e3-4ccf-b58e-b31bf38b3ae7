import 'package:equatable/equatable.dart';
import '../models/mosque_model.dart';

/// Base state for mosque finder
abstract class MosqueFinderState extends Equatable {
  const MosqueFinderState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class MosqueFinderInitial extends MosqueFinderState {}

/// Loading state with progress tracking
class MosqueFinderLoading extends MosqueFinderState {
  final double progress;
  final String message;

  const MosqueFinderLoading({
    this.progress = 0.0,
    this.message = 'Loading...',
  });

  @override
  List<Object?> get props => [progress, message];
}

/// Successfully loaded mosques
class MosqueFinderLoaded extends MosqueFinderState {
  final List<MosqueModel> mosques;
  final double? userLatitude;
  final double? userLongitude;
  final String? userLocation;
  final MosqueSearchFilters filters;
  final bool isMapView;

  const MosqueFinderLoaded({
    required this.mosques,
    this.userLatitude,
    this.userLongitude,
    this.userLocation,
    this.filters = const MosqueSearchFilters(),
    this.isMapView = false,
  });

  MosqueFinderLoaded copyWith({
    List<MosqueModel>? mosques,
    double? userLatitude,
    double? userLongitude,
    String? userLocation,
    MosqueSearchFilters? filters,
    bool? isMapView,
  }) {
    return MosqueFinderLoaded(
      mosques: mosques ?? this.mosques,
      userLatitude: userLatitude ?? this.userLatitude,
      userLongitude: userLongitude ?? this.userLongitude,
      userLocation: userLocation ?? this.userLocation,
      filters: filters ?? this.filters,
      isMapView: isMapView ?? this.isMapView,
    );
  }

  @override
  List<Object?> get props => [
        mosques,
        userLatitude,
        userLongitude,
        userLocation,
        filters,
        isMapView,
      ];
}

/// Error state
class MosqueFinderError extends MosqueFinderState {
  final String message;

  const MosqueFinderError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Location service error (GPS disabled, permissions denied)
class MosqueFinderLocationServiceError extends MosqueFinderState {
  final String message;

  const MosqueFinderLocationServiceError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Search state
class MosqueFinderSearching extends MosqueFinderState {
  final String query;
  final double progress;

  const MosqueFinderSearching({
    required this.query,
    this.progress = 0.0,
  });

  @override
  List<Object?> get props => [query, progress];
}

/// Search results state
class MosqueFinderSearchResults extends MosqueFinderState {
  final List<MosqueModel> results;
  final String query;
  final double? userLatitude;
  final double? userLongitude;

  const MosqueFinderSearchResults({
    required this.results,
    required this.query,
    this.userLatitude,
    this.userLongitude,
  });

  @override
  List<Object?> get props => [results, query, userLatitude, userLongitude];
}

/// Mosque details state
class MosqueFinderDetails extends MosqueFinderState {
  final MosqueModel mosque;
  final double? userLatitude;
  final double? userLongitude;

  const MosqueFinderDetails({
    required this.mosque,
    this.userLatitude,
    this.userLongitude,
  });

  @override
  List<Object?> get props => [mosque, userLatitude, userLongitude];
}
