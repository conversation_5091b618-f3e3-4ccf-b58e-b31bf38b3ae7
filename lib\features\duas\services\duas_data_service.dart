import '../../../core/services/logger_service.dart';
import '../models/dua_models.dart';
import '../data/morning_duas_data.dart';
import '../data/evening_duas_data.dart';
import '../data/night_duas_data.dart';
import '../data/forgiveness_duas_data.dart';
import '../data/protection_duas_data.dart';
import '../data/travel_duas_data.dart';
import '../data/comprehensive_duas_data.dart';
import '../data/quranic_duas_data.dart';
import '../data/anxiety_duas_data.dart';
import '../data/knowledge_duas_data.dart';
import '../data/work_duas_data.dart';
import '../data/daily_life_duas_data.dart';
import '../data/health_duas_data.dart';
import '../data/family_duas_data.dart';
import '../data/gratitude_duas_data.dart';
import '../data/guidance_duas_data.dart';
import 'duas_database_service.dart';

class DuasDataService {
  /// Initialize all duas data
  static Future<void> initializeDuasData() async {
    try {
      LoggerService.duas('📖 DUAS: Starting data initialization...');

      // Clear existing data (for fresh start)
      await DuasDatabaseService.clearAllData();

      // Insert categories
      await _insertCategories();

      // Insert duas for each category
      await _insertMorningDuas();
      await _insertEveningDuas();
      await _insertNightDuas();
      await _insertQuranicDuas();
      await _insertForgivenessDuas();
      await _insertProtectionDuas();
      await _insertParadiseDuas();
      await _insertTravelDuas();
      await _insertHealthDuas();
      await _insertFamilyDuas();
      await _insertGratitudeDuasNew();
      await _insertGuidanceDuasNew();
      await _insertMarriageDuas();
      await _insertChildrenDuas();
      await _insertWealthDuas();
      await _insertWorkDuas();
      await _insertKnowledgeDuas();
      await _insertAnxietyDuas();
      await _insertDailyLifeDuas();

      // Check total count for debugging
      await DuasDatabaseService.getTotalDuasCount();

      LoggerService.duas(
        '📖 DUAS: Data initialization completed successfully!',
      );
    } catch (e) {
      LoggerService.error('❌ DUAS: Error initializing data: $e');
    }
  }

  /// Insert all categories
  static Future<void> _insertCategories() async {
    final categories = [
      DuaCategoryModel(
        id: 'morning',
        name: 'Morning Duas',
        description: 'Start your day with Allah\'s blessings and protection',
        iconPath: 'assets/icons/sunrise.png',
        colorHex: '#FF9800',
        duasCount: 50,
        orderIndex: 1,
      ),
      DuaCategoryModel(
        id: 'evening',
        name: 'Evening Duas',
        description: 'End your day with gratitude and seeking forgiveness',
        iconPath: 'assets/icons/sunset.png',
        colorHex: '#9C27B0', // Changed to brighter purple
        duasCount: 50,
        orderIndex: 2,
      ),
      DuaCategoryModel(
        id: 'night',
        name: 'Night Duas',
        description: 'Peaceful sleep prayers and night protection',
        iconPath: 'assets/icons/night.png',
        colorHex: '#5C6BC0', // Changed to lighter blue-purple
        duasCount: 40,
        orderIndex: 3,
      ),
      DuaCategoryModel(
        id: 'quranic',
        name: 'Quranic Duas',
        description: 'Beautiful supplications directly from the Holy Quran',
        iconPath: 'assets/icons/quran.png',
        colorHex: '#4CAF50',
        duasCount: 60,
        orderIndex: 4,
      ),
      DuaCategoryModel(
        id: 'forgiveness',
        name: 'Forgiveness Duas',
        description: 'Seek Allah\'s mercy and forgiveness for your sins',
        iconPath: 'assets/icons/forgiveness.png',
        colorHex: '#03A9F4', // Changed to lighter blue to avoid conflict
        duasCount: 50,
        orderIndex: 5,
      ),
      DuaCategoryModel(
        id: 'protection',
        name: 'Protection Duas',
        description: 'Shield yourself from evil eye, jinn, and harm',
        iconPath: 'assets/icons/shield.png',
        colorHex: '#F44336',
        duasCount: 50,
        orderIndex: 6,
      ),
      DuaCategoryModel(
        id: 'paradise',
        name: 'Paradise Duas',
        description: 'Supplications seeking Jannah and Allah\'s pleasure',
        iconPath: 'assets/icons/paradise.png',
        colorHex: '#00BCD4',
        duasCount: 40,
        orderIndex: 7,
      ),
      DuaCategoryModel(
        id: 'travel',
        name: 'Travel Duas',
        description: 'Safe journey prayers for all types of travel',
        iconPath: 'assets/icons/travel.png',
        colorHex: '#795548',
        duasCount: 40,
        orderIndex: 8,
      ),
      DuaCategoryModel(
        id: 'illness',
        name: 'Illness & Healing',
        description: 'Prayers for health, healing, and visiting the sick',
        iconPath: 'assets/icons/healing.png',
        colorHex: '#8BC34A',
        duasCount: 40,
        orderIndex: 9,
      ),
      DuaCategoryModel(
        id: 'marriage',
        name: 'Marriage & Family',
        description: 'Duas for spouse, marriage, and family harmony',
        iconPath: 'assets/icons/family.png',
        colorHex: '#E91E63',
        duasCount: 40,
        orderIndex: 10,
      ),
      DuaCategoryModel(
        id: 'children',
        name: 'Children Duas',
        description: 'Prayers for children, pregnancy, and parenting',
        iconPath: 'assets/icons/children.png',
        colorHex: '#FFEB3B',
        duasCount: 40,
        orderIndex: 11,
      ),
      DuaCategoryModel(
        id: 'wealth',
        name: 'Wealth & Success',
        description: 'Seeking halal rizq, success, and financial stability',
        iconPath: 'assets/icons/wealth.png',
        colorHex: '#FF5722',
        duasCount: 40,
        orderIndex: 12,
      ),
      DuaCategoryModel(
        id: 'work',
        name: 'Work & Business',
        description: 'Professional success, meetings, and business growth',
        iconPath: 'assets/icons/work.png',
        colorHex: '#607D8B',
        duasCount: 40,
        orderIndex: 13,
      ),
      DuaCategoryModel(
        id: 'knowledge',
        name: 'Knowledge & Education',
        description: 'Learning, studying, exams, and seeking wisdom',
        iconPath: 'assets/icons/knowledge.png',
        colorHex: '#2196F3', // Changed to brighter blue
        duasCount: 40,
        orderIndex: 14,
      ),
      DuaCategoryModel(
        id: 'anxiety',
        name: 'Anxiety & Stress',
        description: 'Peace of mind, stress relief, and emotional healing',
        iconPath: 'assets/icons/peace.png',
        colorHex: '#009688',
        duasCount: 40,
        orderIndex: 15,
      ),
      DuaCategoryModel(
        id: 'daily_life',
        name: 'Daily Life',
        description: 'Everyday activities, home, eating, and routine prayers',
        iconPath: 'assets/icons/daily.png',
        colorHex: '#E91E63', // Changed to bright pink
        duasCount: 40,
        orderIndex: 16,
      ),
    ];

    for (final category in categories) {
      await DuasDatabaseService.insertCategory(category);
    }
  }

  /// Insert Morning Duas
  static Future<void> _insertMorningDuas() async {
    final morningDuas = MorningDuasData.getMorningDuas();
    LoggerService.duas(
      '📖 DUAS: Inserting ${morningDuas.length} morning duas...',
    );

    for (final dua in morningDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
    LoggerService.duas('📖 DUAS: Completed inserting morning duas');
  }

  /// Insert Evening Duas
  static Future<void> _insertEveningDuas() async {
    final eveningDuas = EveningDuasData.getEveningDuas();
    LoggerService.duas(
      '📖 DUAS: Inserting ${eveningDuas.length} evening duas...',
    );
    for (final dua in eveningDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
    LoggerService.duas('📖 DUAS: Completed inserting evening duas');
  }

  /// Insert Quranic Duas
  static Future<void> _insertQuranicDuas() async {
    final quranicDuas = QuranicDuasData.getQuranicDuas();
    LoggerService.duas(
      '📖 DUAS: Inserting ${quranicDuas.length} Quranic duas...',
    );
    for (final dua in quranicDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
    LoggerService.duas('📖 DUAS: Completed inserting Quranic duas');
  }

  /// Insert Night Duas
  static Future<void> _insertNightDuas() async {
    final nightDuas = NightDuasData.getNightDuas();
    for (final dua in nightDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
  }

  /// Insert Forgiveness Duas
  static Future<void> _insertForgivenessDuas() async {
    final forgivenessDuas = ForgivenessDuasData.getForgivenessDuas();
    for (final dua in forgivenessDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
  }

  /// Insert Protection Duas
  static Future<void> _insertProtectionDuas() async {
    final protectionDuas = ProtectionDuasData.getProtectionDuas();
    for (final dua in protectionDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
  }

  /// Insert Paradise Duas
  static Future<void> _insertParadiseDuas() async {
    final paradiseDuas = ComprehensiveDuasData.getParadiseDuas();
    for (final dua in paradiseDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
  }

  /// Insert Travel Duas
  static Future<void> _insertTravelDuas() async {
    final travelDuas = TravelDuasData.getTravelDuas();
    for (final dua in travelDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
  }

  /// Insert Health & Healing Duas
  static Future<void> _insertHealthDuas() async {
    final healthDuas = HealthDuasData.getHealthDuas();
    LoggerService.duas(
      '📖 DUAS: Inserting ${healthDuas.length} health & healing duas...',
    );
    for (final dua in healthDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
    LoggerService.duas('📖 DUAS: Completed inserting health & healing duas');
  }

  /// Insert Family Duas
  static Future<void> _insertFamilyDuas() async {
    final familyDuas = FamilyDuasData.getFamilyDuas();
    LoggerService.duas(
      '📖 DUAS: Inserting ${familyDuas.length} family duas...',
    );
    for (final dua in familyDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
    LoggerService.duas('📖 DUAS: Completed inserting family duas');
  }

  /// Insert Gratitude Duas
  static Future<void> _insertGratitudeDuasNew() async {
    final gratitudeDuas = GratitudeDuasData.getGratitudeDuas();
    LoggerService.duas(
      '📖 DUAS: Inserting ${gratitudeDuas.length} gratitude duas...',
    );
    for (final dua in gratitudeDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
    LoggerService.duas('📖 DUAS: Completed inserting gratitude duas');
  }

  /// Insert Guidance Duas
  static Future<void> _insertGuidanceDuasNew() async {
    final guidanceDuas = GuidanceDuasData.getGuidanceDuas();
    LoggerService.duas(
      '📖 DUAS: Inserting ${guidanceDuas.length} guidance duas...',
    );
    for (final dua in guidanceDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
    LoggerService.duas('📖 DUAS: Completed inserting guidance duas');
  }

  /// Insert Marriage Duas
  static Future<void> _insertMarriageDuas() async {
    final marriageDuas = ComprehensiveDuasData.getMarriageDuas();
    for (final dua in marriageDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
  }

  /// Insert Children Duas
  static Future<void> _insertChildrenDuas() async {
    final childrenDuas = ComprehensiveDuasData.getChildrenDuas();
    for (final dua in childrenDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
  }

  /// Insert Wealth Duas
  static Future<void> _insertWealthDuas() async {
    final wealthDuas = ComprehensiveDuasData.getWealthDuas();
    for (final dua in wealthDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
  }

  /// Insert Work Duas
  static Future<void> _insertWorkDuas() async {
    final workDuas = WorkDuasData.getWorkDuas();
    LoggerService.duas('📖 DUAS: Inserting ${workDuas.length} work duas...');
    for (final dua in workDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
    LoggerService.duas('📖 DUAS: Completed inserting work duas');
  }

  /// Insert Knowledge Duas
  static Future<void> _insertKnowledgeDuas() async {
    final knowledgeDuas = KnowledgeDuasData.getKnowledgeDuas();
    LoggerService.duas(
      '📖 DUAS: Inserting ${knowledgeDuas.length} knowledge duas...',
    );
    for (final dua in knowledgeDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
    LoggerService.duas('📖 DUAS: Completed inserting knowledge duas');
  }

  /// Insert Anxiety Duas
  static Future<void> _insertAnxietyDuas() async {
    final anxietyDuas = AnxietyDuasData.getAnxietyDuas();
    LoggerService.duas(
      '📖 DUAS: Inserting ${anxietyDuas.length} anxiety duas...',
    );
    for (final dua in anxietyDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
    LoggerService.duas('📖 DUAS: Completed inserting anxiety duas');
  }

  /// Insert Daily Life Duas
  static Future<void> _insertDailyLifeDuas() async {
    final dailyLifeDuas = DailyLifeDuasData.getDailyLifeDuas();
    LoggerService.duas(
      '📖 DUAS: Inserting ${dailyLifeDuas.length} daily life duas...',
    );
    for (final dua in dailyLifeDuas) {
      await DuasDatabaseService.insertDua(dua);
    }
    LoggerService.duas('📖 DUAS: Completed inserting daily life duas');
  }
}
