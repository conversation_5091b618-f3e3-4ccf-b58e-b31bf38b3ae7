import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/dua_models.dart';
import '../cubit/duas_cubit.dart';

class DuasCategoryView extends StatelessWidget {
  final DuaCategoryModel category;
  final List<DuaModel> duas;

  const DuasCategoryView({
    super.key,
    required this.category,
    required this.duas,
  });

  @override
  Widget build(BuildContext context) {
    final color = Color(int.parse(category.colorHex.replaceFirst('#', '0xFF')));
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final displayColor = isDarkMode ? _adjustColorForDarkMode(color) : color;

    return Column(
      children: [
        // Category Header - Compact
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(12.w),
          margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          decoration: BoxDecoration(
            color: displayColor.withValues(alpha: 0.12),
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: displayColor.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: displayColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  _getCategoryIcon(category.id),
                  color: displayColor,
                  size: 20.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      category.name,
                      style: AppTextStyles.labelLarge.copyWith(
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      '${duas.length} duas',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: displayColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Duas List
        Expanded(
          child: ListView.builder(
            key: PageStorageKey<String>('duas_category_${category.id}'),
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            itemCount: duas.length,
            itemBuilder: (context, index) {
              final dua = duas[index];
              return _buildDuaCard(context, dua, color);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDuaCard(
    BuildContext context,
    DuaModel dua,
    Color categoryColor,
  ) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap:
              () => context.read<DuasCubit>().viewDua(
                dua,
                fromCategory: category,
              ),
          borderRadius: BorderRadius.circular(16.r),
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(
                color: categoryColor.withValues(alpha: 0.2),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: categoryColor.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 6),
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: Colors.black.withValues(
                    alpha: isDarkMode ? 0.3 : 0.06,
                  ),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header with title and favorite
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          dua.title,
                          style: AppTextStyles.labelMedium.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(
                        width: 32.w,
                        height: 32.h,
                        child: IconButton(
                          onPressed:
                              () =>
                                  context.read<DuasCubit>().toggleFavorite(dua),
                          icon: Icon(
                            dua.isFavorite
                                ? Icons.favorite
                                : Icons.favorite_border,
                            color: dua.isFavorite ? Colors.red : Colors.grey,
                            size: 18.sp,
                          ),
                          padding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 8.h),

                  // Arabic text preview
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                      color: categoryColor.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      dua.arabicText.length > 80
                          ? '${dua.arabicText.substring(0, 80)}...'
                          : dua.arabicText,
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontFamily: 'Arabic',
                        height: 1.6,
                        color: categoryColor,
                      ),
                      textAlign: TextAlign.right,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  SizedBox(height: 8.h),

                  // English translation preview
                  Text(
                    dua.englishTranslation.length > 100
                        ? '${dua.englishTranslation.substring(0, 100)}...'
                        : dua.englishTranslation,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.grey[600],
                      height: 1.3,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  SizedBox(height: 8.h),

                  // Benefits and action row
                  Row(
                    children: [
                      if (dua.benefits.isNotEmpty) ...[
                        Icon(Icons.star, color: Colors.amber, size: 14.sp),
                        SizedBox(width: 4.w),
                        Expanded(
                          child: Text(
                            dua.benefits.first,
                            style: AppTextStyles.bodySmall.copyWith(
                              color: Colors.amber[700],
                              fontWeight: FontWeight.w500,
                              fontSize: 11.sp,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],

                      // Quick action button
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: categoryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(6.r),
                        ),
                        child: Text(
                          'Read More',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: categoryColor,
                            fontWeight: FontWeight.w600,
                            fontSize: 10.sp,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String categoryId) {
    switch (categoryId) {
      case 'morning':
        return Icons.wb_sunny;
      case 'evening':
        return Icons.nights_stay;
      case 'night':
        return Icons.bedtime;
      case 'quranic':
        return Icons.menu_book;
      case 'forgiveness':
        return Icons.favorite;
      case 'protection':
        return Icons.shield;
      case 'paradise':
        return Icons.cloud;
      case 'travel':
        return Icons.flight;
      case 'illness':
        return Icons.healing;
      case 'marriage':
        return Icons.favorite_border;
      case 'children':
        return Icons.child_care;
      case 'wealth':
        return Icons.attach_money;
      case 'work':
        return Icons.work;
      case 'knowledge':
        return Icons.school;
      case 'anxiety':
        return Icons.self_improvement;
      case 'daily_life':
        return Icons.home;
      default:
        return Icons.menu_book;
    }
  }

  /// Adjust color brightness for dark mode visibility
  Color _adjustColorForDarkMode(Color color) {
    final hsl = HSLColor.fromColor(color);

    // Increase lightness for better visibility in dark mode
    final adjustedLightness = (hsl.lightness + 0.3).clamp(0.0, 1.0);

    // Increase saturation slightly for more vibrant colors
    final adjustedSaturation = (hsl.saturation + 0.1).clamp(0.0, 1.0);

    return hsl
        .withLightness(adjustedLightness)
        .withSaturation(adjustedSaturation)
        .toColor();
  }
}
