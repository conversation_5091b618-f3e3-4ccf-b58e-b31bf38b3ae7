import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:share_plus/share_plus.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/dua_models.dart';
import '../cubit/duas_cubit.dart';

class DuasDetailView extends StatelessWidget {
  final DuaModel dua;
  final DuaProgressModel? progress;
  final bool isPlaying;

  const DuasDetailView({
    super.key,
    required this.dua,
    this.progress,
    this.isPlaying = false,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and actions
          _buildHeader(context),

          SizedBox(height: 24.h),

          // Arabic Text
          _buildArabicSection(context),

          SizedBox(height: 24.h),

          // Transliteration
          _buildTransliterationSection(context),

          SizedBox(height: 24.h),

          // English Translation
          _buildTranslationSection(context),

          SizedBox(height: 24.h),

          // Simple Explanation
          _buildExplanationSection(context),

          SizedBox(height: 24.h),

          // Benefits
          _buildBenefitsSection(context),

          SizedBox(height: 24.h),

          // When to Recite
          _buildWhenToReciteSection(context),

          SizedBox(height: 24.h),

          // Real Life Examples
          _buildExamplesSection(context),

          SizedBox(height: 24.h),

          // Hadith Reference
          _buildReferenceSection(context),

          SizedBox(height: 24.h),

          // Action Buttons
          _buildActionButtons(context),

          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            dua.title,
            style: AppTextStyles.headlineSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        IconButton(
          onPressed: () => context.read<DuasCubit>().toggleFavorite(dua),
          icon: Icon(
            dua.isFavorite ? Icons.favorite : Icons.favorite_border,
            color: dua.isFavorite ? Colors.red : Colors.grey,
            size: 28.sp,
          ),
        ),
      ],
    );
  }

  Widget _buildArabicSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Arabic Text',
      icon: Icons.menu_book,
      color: AppColors.primaryGreen,
      child: Text(
        dua.arabicText,
        style: AppTextStyles.headlineMedium.copyWith(
          fontFamily: 'Amiri',
          fontSize: 28.sp,
          height: 2.2,
          color: AppColors.primaryGreen,
          fontWeight: FontWeight.w600,
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.rtl,
      ),
    );
  }

  Widget _buildTransliterationSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Pronunciation Guide',
      icon: Icons.record_voice_over,
      color: Colors.blue,
      child: Text(
        dua.transliteration,
        style: AppTextStyles.bodyLarge.copyWith(
          fontStyle: FontStyle.italic,
          height: 1.8,
          color: Colors.blue[700],
          fontSize: 16.sp,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildTranslationSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'English Translation',
      icon: Icons.translate,
      color: Colors.purple,
      child: Text(
        dua.englishTranslation,
        style: AppTextStyles.bodyLarge.copyWith(
          height: 1.6,
          color: Colors.purple[700],
        ),
      ),
    );
  }

  Widget _buildExplanationSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Simple Explanation',
      icon: Icons.lightbulb,
      color: Colors.orange,
      child: Text(
        dua.simpleExplanation,
        style: AppTextStyles.bodyMedium.copyWith(
          height: 1.5,
          color: Colors.grey[700],
        ),
      ),
    );
  }

  Widget _buildBenefitsSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Benefits & Blessings',
      icon: Icons.star,
      color: Colors.amber,
      child: Column(
        children:
            dua.benefits.map((benefit) {
              return Padding(
                padding: EdgeInsets.only(bottom: 8.h),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(Icons.star, color: Colors.amber, size: 16.sp),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(
                        benefit,
                        style: AppTextStyles.bodyMedium.copyWith(height: 1.4),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildWhenToReciteSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'When to Recite',
      icon: Icons.schedule,
      color: Colors.teal,
      child: Column(
        children:
            dua.whenToRecite.map((time) {
              return Padding(
                padding: EdgeInsets.only(bottom: 8.h),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(Icons.access_time, color: Colors.teal, size: 16.sp),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(
                        time,
                        style: AppTextStyles.bodyMedium.copyWith(height: 1.4),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildExamplesSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Real Life Examples',
      icon: Icons.people,
      color: Colors.green,
      child: Column(
        children:
            dua.realLifeExamples.map((example) {
              return Container(
                margin: EdgeInsets.only(bottom: 12.h),
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(
                    color: Colors.green.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Text(
                  example,
                  style: AppTextStyles.bodyMedium.copyWith(
                    height: 1.4,
                    color: Colors.green[700],
                  ),
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildReferenceSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Islamic Reference',
      icon: Icons.book,
      color: Colors.brown,
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: Colors.brown.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Text(
          dua.hadithReference,
          style: AppTextStyles.bodyMedium.copyWith(
            height: 1.4,
            color: Colors.brown[700],
            fontStyle: FontStyle.italic,
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        children: [
          // Primary Action Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => context.read<DuasCubit>().markDuaRecited(dua),
              icon: Icon(Icons.check_circle, size: 22.sp),
              label: Text(
                'Mark as Recited',
                style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGreen,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 16.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16.r),
                ),
                elevation: 2,
                shadowColor: AppColors.primaryGreen.withValues(alpha: 0.3),
              ),
            ),
          ),

          SizedBox(height: 16.h),

          // Secondary Actions Row
          Row(
            children: [
              Expanded(
                child: _buildSecondaryButton(
                  context,
                  icon: Icons.share,
                  label: 'Share',
                  color: Colors.blue,
                  onPressed: () => _shareDua(context),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildSecondaryButton(
                  context,
                  icon: Icons.copy,
                  label: 'Copy',
                  color: Colors.orange,
                  onPressed: () => _copyDua(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSecondaryButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 18.sp),
      label: Text(
        label,
        style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500),
      ),
      style: OutlinedButton.styleFrom(
        foregroundColor: color,
        side: BorderSide(color: color.withValues(alpha: 0.5), width: 1.5),
        padding: EdgeInsets.symmetric(vertical: 12.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        backgroundColor: color.withValues(alpha: 0.05),
      ),
    );
  }

  /// Copy dua to clipboard
  void _copyDua(BuildContext context) {
    final buffer = StringBuffer();
    buffer.writeln('🤲 ${dua.title}');
    buffer.writeln();
    buffer.writeln('📖 Arabic:');
    buffer.writeln(dua.arabicText);
    buffer.writeln();
    buffer.writeln('🔤 Pronunciation:');
    buffer.writeln(dua.transliteration);
    buffer.writeln();
    buffer.writeln('🌍 Translation:');
    buffer.writeln(dua.englishTranslation);

    Clipboard.setData(ClipboardData(text: buffer.toString()));

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Dua copied to clipboard'),
        backgroundColor: AppColors.primaryGreen,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.r),
        ),
      ),
    );
  }

  /// Share dua with beautiful formatting
  void _shareDua(BuildContext context) {
    final shareText = _formatDuaForSharing();

    Share.share(shareText, subject: '🤲 ${dua.title} - Islamic Dua');
  }

  /// Format dua content for sharing
  String _formatDuaForSharing() {
    final buffer = StringBuffer();

    // Title with emoji
    buffer.writeln('🤲 ${dua.title}');
    buffer.writeln('=' * 40);
    buffer.writeln();

    // Arabic text
    buffer.writeln('📖 Arabic:');
    buffer.writeln(dua.arabicText);
    buffer.writeln();

    // Transliteration
    buffer.writeln('🔤 Transliteration:');
    buffer.writeln(dua.transliteration);
    buffer.writeln();

    // English translation
    buffer.writeln('🌍 Translation:');
    buffer.writeln(dua.englishTranslation);
    buffer.writeln();

    // Simple explanation
    buffer.writeln('💡 Explanation:');
    buffer.writeln(dua.simpleExplanation);
    buffer.writeln();

    // Benefits (first 3)
    if (dua.benefits.isNotEmpty) {
      buffer.writeln('✨ Benefits:');
      final benefitsToShow = dua.benefits.take(3);
      for (int i = 0; i < benefitsToShow.length; i++) {
        buffer.writeln('${i + 1}. ${benefitsToShow.elementAt(i)}');
      }
      if (dua.benefits.length > 3) {
        buffer.writeln('... and ${dua.benefits.length - 3} more benefits');
      }
      buffer.writeln();
    }

    // When to recite (first 2)
    if (dua.whenToRecite.isNotEmpty) {
      buffer.writeln('⏰ When to recite:');
      final timesToShow = dua.whenToRecite.take(2);
      for (int i = 0; i < timesToShow.length; i++) {
        buffer.writeln('• ${timesToShow.elementAt(i)}');
      }
      if (dua.whenToRecite.length > 2) {
        buffer.writeln('... and more occasions');
      }
      buffer.writeln();
    }

    // Hadith reference
    if (dua.hadithReference.isNotEmpty) {
      buffer.writeln('📚 Reference:');
      buffer.writeln(dua.hadithReference);
      buffer.writeln();
    }

    // App promotion
    buffer.writeln('─' * 40);
    buffer.writeln('🌙 Shared from Noor Islamic App');
    buffer.writeln('📱 Download for more authentic Islamic duas');

    return buffer.toString();
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required Widget child,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(bottom: 4.h),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: isDarkMode ? 0.2 : 0.04),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(6.w),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(icon, color: color, size: 20.sp),
                ),
                SizedBox(width: 12.w),
                Text(
                  title,
                  style: AppTextStyles.labelLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Padding(padding: EdgeInsets.all(16.w), child: child),
        ],
      ),
    );
  }
}
