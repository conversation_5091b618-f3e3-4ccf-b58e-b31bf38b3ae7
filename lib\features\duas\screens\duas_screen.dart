import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../presentation/widgets/gradient_background.dart';
import '../../../presentation/widgets/loading_widget.dart';
import '../cubit/duas_cubit.dart';
import '../cubit/duas_state.dart';
import '../widgets/duas_categories_view.dart';
import '../widgets/duas_category_view.dart';
import '../widgets/duas_detail_view.dart';
import '../widgets/duas_search_view.dart';
import '../widgets/duas_favorites_view.dart';
import '../widgets/duas_recent_view.dart';

class DuasScreen extends StatefulWidget {
  const DuasScreen({super.key});

  @override
  State<DuasScreen> createState() => _DuasScreenState();
}

class _DuasScreenState extends State<DuasScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    // Initialize duas when screen loads
    context.read<DuasCubit>().initialize();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          _handleBackPress();
        }
      },
      child: Scaffold(
        body: GradientBackground(
          child: SafeArea(
            child: Column(
              children: [
                // Header
                _buildHeader(),

                // Search Bar
                if (_isSearching) _buildSearchBar(),

                // Content
                Expanded(
                  child: BlocBuilder<DuasCubit, DuasState>(
                    builder: (context, state) {
                      if (state is DuasLoading) {
                        return LoadingWidget(
                          message: state.message,
                          progress: state.progress,
                        );
                      } else if (state is DuasError) {
                        return _buildErrorView(state.message);
                      } else if (state is DuasCategoriesLoaded) {
                        return DuasCategoriesView(
                          categories: state.categories,
                          favoriteDuas: state.favoriteDuas,
                          recentDuas: state.recentDuas,
                        );
                      } else if (state is DuasCategoryDuasLoaded) {
                        return DuasCategoryView(
                          category: state.category,
                          duas: state.duas,
                        );
                      } else if (state is DuasViewing) {
                        return DuasDetailView(
                          dua: state.dua,
                          progress: state.progress,
                          isPlaying: state.isPlaying,
                        );
                      } else if (state is DuasSearchResults) {
                        return DuasSearchView(
                          query: state.query,
                          results: state.results,
                        );
                      } else if (state is DuasFavorites) {
                        return DuasFavoritesView(
                          favoriteDuas: state.favoriteDuas,
                        );
                      } else if (state is DuasRecent) {
                        return DuasRecentView(recentDuas: state.recentDuas);
                      }

                      return const Center(child: CircularProgressIndicator());
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(24.r),
          bottomRight: Radius.circular(24.r),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // Back button with better styling
          _buildHeaderButton(
            icon: Icons.arrow_back_ios,
            onPressed: _handleBackPress,
          ),

          if (!_isSearching) ...[
            SizedBox(width: 16.w),
            // Title - More compact
            Expanded(
              child: BlocBuilder<DuasCubit, DuasState>(
                builder: (context, state) {
                  String title = 'Duas';

                  if (state is DuasCategoryDuasLoaded) {
                    title = state.category.name;
                  } else if (state is DuasSearchResults) {
                    title = 'Search Results';
                  } else if (state is DuasFavorites) {
                    title = 'Favorites';
                  } else if (state is DuasRecent) {
                    title = 'Recent Duas';
                  } else if (state is DuasViewing) {
                    title = 'Dua Details';
                  }

                  return Text(
                    title,
                    style: AppTextStyles.headlineSmall.copyWith(
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black87,
                      fontWeight: FontWeight.bold,
                      fontSize: 20.sp,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  );
                },
              ),
            ),
          ],

          // Action buttons
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeaderButton(
                icon: _isSearching ? Icons.close : Icons.search,
                onPressed: () {
                  // Dismiss keyboard first to prevent pixel errors
                  FocusScope.of(context).unfocus();

                  // Small delay to ensure smooth transition
                  Future.delayed(const Duration(milliseconds: 100), () {
                    setState(() {
                      _isSearching = !_isSearching;
                      if (!_isSearching) {
                        _searchController.clear();
                        context.read<DuasCubit>().loadCategories();
                      }
                    });
                  });
                },
              ),
              SizedBox(width: 8.w),
              _buildHeaderButton(
                icon: Icons.favorite,
                onPressed: () => context.read<DuasCubit>().showFavorites(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(14.r),
        child: Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: AppColors.primaryGreen.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(14.r),
            border: Border.all(
              color: AppColors.primaryGreen.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.primaryGreen.withValues(alpha: 0.1),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(icon, color: AppColors.primaryGreen, size: 20.sp),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: AppColors.primaryGreen.withValues(alpha: 0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGreen.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        autofocus: true,
        style: AppTextStyles.bodyMedium.copyWith(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black87,
        ),
        decoration: InputDecoration(
          hintText: 'Search by title, meaning, benefits, occasions...',
          hintStyle: AppTextStyles.bodyMedium.copyWith(
            color: Colors.grey[500],
            fontSize: 14.sp,
          ),
          prefixIcon: Container(
            padding: EdgeInsets.all(12.w),
            child: Icon(
              Icons.search_rounded,
              color: AppColors.primaryGreen,
              size: 22.sp,
            ),
          ),
          suffixIcon:
              _searchController.text.isNotEmpty
                  ? Container(
                    padding: EdgeInsets.all(8.w),
                    child: IconButton(
                      onPressed: () {
                        _searchController.clear();
                        context.read<DuasCubit>().loadCategories();
                      },
                      icon: Icon(
                        Icons.clear_rounded,
                        color: Colors.grey[600],
                        size: 20.sp,
                      ),
                      padding: EdgeInsets.all(4.w),
                      constraints: BoxConstraints(
                        minWidth: 32.w,
                        minHeight: 32.h,
                      ),
                    ),
                  )
                  : null,
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 16.h,
          ),
        ),
        onChanged: (query) {
          // Debounce search to improve performance
          if (_debounceTimer?.isActive ?? false) _debounceTimer!.cancel();
          _debounceTimer = Timer(const Duration(milliseconds: 300), () {
            context.read<DuasCubit>().searchDuas(query);
          });
        },
      ),
    );
  }

  Widget _buildErrorView(String message) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
            SizedBox(height: 16.h),
            Text(
              'Oops! Something went wrong',
              style: AppTextStyles.headlineSmall.copyWith(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            ElevatedButton(
              onPressed: () => context.read<DuasCubit>().initialize(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGreen,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Text(
                'Try Again',
                style: AppTextStyles.labelLarge.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleBackPress() {
    final currentState = context.read<DuasCubit>().state;

    if (currentState is DuasCategoriesLoaded) {
      // Go back to Islamic Features screen
      Navigator.of(context).pop();
    } else {
      // Use smart back navigation
      context.read<DuasCubit>().goBack();
    }
  }
}
