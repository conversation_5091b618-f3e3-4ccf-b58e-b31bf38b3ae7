import 'dart:math' as math;
import '../../../core/services/logger_service.dart';

class QiblaCalculationService {
  // Kaaba coordinates (most accurate)
  static const double _kaabaLatitude = 21.4224779;
  static const double _kaabaLongitude = 39.8262119;

  /// Calculate Qibla direction using great circle distance formula
  /// Returns bearing in degrees (0-360) from North
  static double calculateQiblaDirection({
    required double userLatitude,
    required double userLongitude,
  }) {
    try {
      LoggerService.qibla(
        '🧭 QIBLA: Calculating direction from ($userLatitude, $userLongitude)',
      );

      // Convert degrees to radians
      final double lat1Rad = _degreesToRadians(userLatitude);
      final double lon1Rad = _degreesToRadians(userLongitude);
      final double lat2Rad = _degreesToRadians(_kaabaLatitude);
      final double lon2Rad = _degreesToRadians(_kaabaLongitude);

      // Calculate difference in longitude
      final double deltaLon = lon2Rad - lon1Rad;

      // Calculate bearing using great circle formula
      final double y = math.sin(deltaLon) * math.cos(lat2Rad);
      final double x =
          math.cos(lat1Rad) * math.sin(lat2Rad) -
          math.sin(lat1Rad) * math.cos(lat2Rad) * math.cos(deltaLon);

      // Calculate initial bearing
      double bearing = math.atan2(y, x);

      // Convert to degrees and normalize to 0-360
      bearing = _radiansToDegrees(bearing);
      bearing = (bearing + 360) % 360;

      LoggerService.qibla(
        '🧭 QIBLA: Calculated bearing: ${bearing.toStringAsFixed(2)}°',
      );

      return bearing;
    } catch (e) {
      LoggerService.error('❌ QIBLA: Error calculating direction: $e');
      return 0.0;
    }
  }

  /// Calculate distance to Kaaba in kilometers
  static double calculateDistanceToKaaba({
    required double userLatitude,
    required double userLongitude,
  }) {
    try {
      const double earthRadius = 6371.0; // Earth's radius in kilometers

      // Convert degrees to radians
      final double lat1Rad = _degreesToRadians(userLatitude);
      final double lon1Rad = _degreesToRadians(userLongitude);
      final double lat2Rad = _degreesToRadians(_kaabaLatitude);
      final double lon2Rad = _degreesToRadians(_kaabaLongitude);

      // Haversine formula
      final double deltaLat = lat2Rad - lat1Rad;
      final double deltaLon = lon2Rad - lon1Rad;

      final double a =
          math.sin(deltaLat / 2) * math.sin(deltaLat / 2) +
          math.cos(lat1Rad) *
              math.cos(lat2Rad) *
              math.sin(deltaLon / 2) *
              math.sin(deltaLon / 2);

      final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
      final double distance = earthRadius * c;

      LoggerService.qibla(
        '🧭 QIBLA: Distance to Kaaba: ${distance.toStringAsFixed(2)} km',
      );

      return distance;
    } catch (e) {
      LoggerService.error('❌ QIBLA: Error calculating distance: $e');
      return 0.0;
    }
  }

  /// Get magnetic declination for location (simplified calculation)
  /// In a production app, you'd use a proper magnetic declination API
  static double getMagneticDeclination({
    required double latitude,
    required double longitude,
  }) {
    try {
      // Simplified magnetic declination calculation
      // This is a basic approximation - for production use a proper API

      // Basic declination based on location (very simplified)
      double declination = 0.0;

      // Rough approximation for different regions
      if (latitude > 60) {
        declination = -15.0; // Arctic regions
      } else if (latitude > 30) {
        declination = longitude > 0 ? 5.0 : -10.0; // Northern regions
      } else if (latitude > 0) {
        declination = longitude > 0 ? 2.0 : -5.0; // Tropical regions
      } else if (latitude > -30) {
        declination = longitude > 0 ? -2.0 : 5.0; // Southern tropical
      } else {
        declination = 15.0; // Southern regions
      }

      LoggerService.qibla(
        '🧭 QIBLA: Magnetic declination: ${declination.toStringAsFixed(2)}°',
      );

      return declination;
    } catch (e) {
      LoggerService.error(
        '❌ QIBLA: Error calculating magnetic declination: $e',
      );
      return 0.0;
    }
  }

  /// Adjust compass bearing for magnetic declination
  static double adjustForMagneticDeclination({
    required double magneticBearing,
    required double declination,
  }) {
    // True bearing = Magnetic bearing + Declination
    double trueBearing = (magneticBearing + declination + 360) % 360;
    return trueBearing;
  }

  /// Calculate the difference between current heading and Qibla direction
  static double calculateQiblaOffset({
    required double currentHeading,
    required double qiblaDirection,
  }) {
    double offset = qiblaDirection - currentHeading;

    // Normalize to -180 to +180 range
    while (offset > 180) {
      offset -= 360;
    }
    while (offset < -180) {
      offset += 360;
    }

    return offset;
  }

  /// Check if user is facing Qibla (within tolerance)
  static bool isFacingQibla({
    required double currentHeading,
    required double qiblaDirection,
    double tolerance = 5.0, // degrees
  }) {
    final double offset =
        calculateQiblaOffset(
          currentHeading: currentHeading,
          qiblaDirection: qiblaDirection,
        ).abs();

    return offset <= tolerance;
  }

  /// Get formatted direction description
  static String getDirectionDescription(double bearing) {
    const List<String> directions = [
      'N',
      'NNE',
      'NE',
      'ENE',
      'E',
      'ESE',
      'SE',
      'SSE',
      'S',
      'SSW',
      'SW',
      'WSW',
      'W',
      'WNW',
      'NW',
      'NNW',
    ];

    int index = ((bearing + 11.25) / 22.5).floor() % 16;
    return directions[index];
  }

  /// Convert degrees to radians
  static double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180.0);
  }

  /// Convert radians to degrees
  static double _radiansToDegrees(double radians) {
    return radians * (180.0 / math.pi);
  }
}
