import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/quran_models.dart';

class QuranSearchView extends StatelessWidget {
  final List<AyahModel> results;
  final String query;
  final QuranReadingPreferences preferences;

  const QuranSearchView({
    super.key,
    required this.results,
    required this.query,
    required this.preferences,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search results header
        Padding(
          padding: EdgeInsets.all(20.w),
          child: Text(
            'Found ${results.length} results for "$query"',
            style: AppTextStyles.titleMedium.copyWith(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black87,
            ),
          ),
        ),
        
        // Results list
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            itemCount: results.length,
            itemBuilder: (context, index) {
              final ayah = results[index];
              return _buildSearchResultCard(context, ayah);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResultCard(BuildContext context, AyahModel ayah) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.white.withValues(alpha: 0.1)
            : Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white.withValues(alpha: 0.2)
              : Colors.black.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Ayah reference
          Text(
            'Ayah ${ayah.numberInSurah}',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.primaryGreen,
              fontWeight: FontWeight.w600,
            ),
          ),
          
          SizedBox(height: 8.h),
          
          // Arabic text
          if (preferences.showArabic)
            Text(
              ayah.text,
              style: AppTextStyles.titleMedium.copyWith(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black87,
                fontSize: preferences.arabicFontSize.sp,
              ),
              textDirection: TextDirection.rtl,
            ),
          
          // Translation
          if (preferences.showTranslation && ayah.translations.isNotEmpty) ...[
            SizedBox(height: 8.h),
            Text(
              ayah.translations.first.text,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white.withValues(alpha: 0.8)
                    : Colors.black.withValues(alpha: 0.7),
                fontSize: preferences.translationFontSize.sp,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
