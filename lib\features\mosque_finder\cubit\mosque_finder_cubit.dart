import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../../../core/services/logger_service.dart';
import '../models/mosque_model.dart';
import '../services/overpass_api_service.dart';
import 'mosque_finder_state.dart';

// Custom exceptions for better error handling
class LocationServiceException implements Exception {
  final String message;
  const LocationServiceException(this.message);
  @override
  String toString() => message;
}

/// Cubit for managing mosque finder functionality
class MosqueFinderCubit extends Cubit<MosqueFinderState> {
  MosqueFinderCubit() : super(MosqueFinderInitial());

  // Current user location
  Position? _currentPosition;
  String? _currentLocationName;
  
  // Search and filter state
  MosqueSearchFilters _currentFilters = const MosqueSearchFilters();
  List<MosqueModel> _allMosques = [];
  bool _isMapView = false;

  /// Initialize mosque finder with location detection
  Future<void> initializeMosqueFinder() async {
    try {
      emit(const MosqueFinderLoading(
        progress: 0.0,
        message: 'Starting mosque finder...',
      ));
      LoggerService.mosque('🕌 MOSQUE: Initializing mosque finder...');

      // Step 1: Check API availability (0-20%)
      emit(const MosqueFinderLoading(
        progress: 0.1,
        message: 'Checking service availability...',
      ));
      
      final isApiAvailable = await OverpassApiService.checkApiAvailability();
      if (!isApiAvailable) {
        emit(const MosqueFinderError(
          'Mosque finder service is currently unavailable. Please try again later.',
        ));
        return;
      }
      
      emit(const MosqueFinderLoading(
        progress: 0.2,
        message: 'Service available',
      ));

      // Step 2: Get location permissions and GPS (20-60%)
      emit(const MosqueFinderLoading(
        progress: 0.3,
        message: 'Getting location permissions...',
      ));
      
      await _getCurrentLocation();
      
      emit(const MosqueFinderLoading(
        progress: 0.6,
        message: 'Location found',
      ));

      // Step 3: Fetch nearby mosques (60-90%)
      emit(const MosqueFinderLoading(
        progress: 0.7,
        message: 'Finding nearby mosques...',
      ));
      
      await _fetchNearbyMosques();
      
      emit(const MosqueFinderLoading(
        progress: 0.9,
        message: 'Loading complete',
      ));

      // Step 4: Complete initialization (90-100%)
      emit(MosqueFinderLoaded(
        mosques: _allMosques,
        userLatitude: _currentPosition?.latitude,
        userLongitude: _currentPosition?.longitude,
        userLocation: _currentLocationName,
        filters: _currentFilters,
        isMapView: _isMapView,
      ));

      LoggerService.mosque('🕌 MOSQUE: Initialization complete with ${_allMosques.length} mosques');
      
    } catch (e) {
      LoggerService.error('❌ MOSQUE: Initialization error: $e');
      
      // Provide specific error messages based on exception type
      if (e is LocationServiceException) {
        emit(MosqueFinderLocationServiceError(e.message));
      } else {
        emit(MosqueFinderError('Failed to initialize mosque finder: ${e.toString()}'));
      }
    }
  }

  /// Get current location with proper error handling
  Future<void> _getCurrentLocation() async {
    try {
      LoggerService.mosque('🕌 MOSQUE: Getting current location...');

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw LocationServiceException(
          'Location services are disabled. Please turn on GPS in your device settings to find nearby mosques.',
        );
      }

      // Check permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw LocationServiceException(
            'Location permission is required to find nearby mosques. Please allow location access.',
          );
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw LocationServiceException(
          'Location permissions are permanently denied. Please enable location access in your device settings.',
        );
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 30),
        ),
      );
      _currentPosition = position;

      // Get location name using geocoding
      try {
        final placemarks = await placemarkFromCoordinates(
          position.latitude,
          position.longitude,
        );

        if (placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          final city = placemark.locality ?? placemark.subAdministrativeArea ?? 'Unknown';
          final state = placemark.administrativeArea ?? 'Unknown';
          final country = placemark.country ?? 'Unknown';
          _currentLocationName = '$city, $state, $country';
        } else {
          _currentLocationName = 'Unknown Location';
        }
      } catch (e) {
        LoggerService.mosque('⚠️ MOSQUE: Could not get location name: $e');
        _currentLocationName = 'Current Location';
      }

      LoggerService.mosque(
        '🕌 MOSQUE: Location: $_currentLocationName (${position.latitude}, ${position.longitude})',
      );
      
    } catch (e) {
      LoggerService.error('❌ MOSQUE: Location error: $e');
      
      // Convert location errors to user-friendly messages
      if (e is LocationServiceException) {
        rethrow; // Already user-friendly
      } else if (e.toString().contains('location') || e.toString().contains('GPS')) {
        throw LocationServiceException(
          'Unable to get your location. Please ensure GPS is enabled and try again.',
        );
      } else {
        throw Exception('Unable to get current location: ${e.toString()}');
      }
    }
  }

  /// Fetch nearby mosques using current location
  Future<void> _fetchNearbyMosques() async {
    if (_currentPosition == null) {
      throw Exception('Location not available');
    }

    try {
      final mosques = await OverpassApiService.fetchMosquesNearLocation(
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
        radiusKm: _currentFilters.maxDistance,
      );

      _allMosques = _applyFilters(mosques);
      
    } catch (e) {
      LoggerService.error('❌ MOSQUE: Error fetching mosques: $e');
      throw Exception('Unable to fetch nearby mosques: ${e.toString()}');
    }
  }

  /// Apply filters to mosque list
  List<MosqueModel> _applyFilters(List<MosqueModel> mosques) {
    return mosques.where((mosque) {
      // Distance filter
      if (mosque.distanceFromUser != null && 
          mosque.distanceFromUser! > _currentFilters.maxDistance) {
        return false;
      }

      // Facilities filter
      if (_currentFilters.requiredFacilities.isNotEmpty) {
        final hasAllFacilities = _currentFilters.requiredFacilities
            .every((facility) => mosque.facilities.contains(facility));
        if (!hasAllFacilities) return false;
      }

      // Denomination filter
      if (_currentFilters.denomination != null &&
          mosque.denomination != _currentFilters.denomination) {
        return false;
      }

      // Open now filter
      if (_currentFilters.openNow && !mosque.isOpen) {
        return false;
      }

      return true;
    }).toList();
  }

  /// Search mosques by query
  Future<void> searchMosques(String query) async {
    if (query.trim().isEmpty) {
      // Return to normal loaded state
      emit(MosqueFinderLoaded(
        mosques: _allMosques,
        userLatitude: _currentPosition?.latitude,
        userLongitude: _currentPosition?.longitude,
        userLocation: _currentLocationName,
        filters: _currentFilters,
        isMapView: _isMapView,
      ));
      return;
    }

    try {
      emit(MosqueFinderSearching(query: query, progress: 0.5));
      
      if (_currentPosition == null) {
        await _getCurrentLocation();
      }

      final results = await OverpassApiService.searchMosques(
        query: query,
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
        radiusKm: _currentFilters.maxDistance,
      );

      final filteredResults = _applyFilters(results);

      emit(MosqueFinderSearchResults(
        results: filteredResults,
        query: query,
        userLatitude: _currentPosition?.latitude,
        userLongitude: _currentPosition?.longitude,
      ));

    } catch (e) {
      LoggerService.error('❌ MOSQUE: Search error: $e');
      emit(MosqueFinderError('Unable to search mosques: ${e.toString()}'));
    }
  }

  /// Update search filters
  void updateFilters(MosqueSearchFilters newFilters) {
    _currentFilters = newFilters;
    
    // Re-apply filters to current mosque list
    final filteredMosques = _applyFilters(_allMosques);
    
    final currentState = state;
    if (currentState is MosqueFinderLoaded) {
      emit(currentState.copyWith(
        mosques: filteredMosques,
        filters: newFilters,
      ));
    }
  }

  /// Toggle between map and list view
  void toggleView() {
    _isMapView = !_isMapView;
    
    final currentState = state;
    if (currentState is MosqueFinderLoaded) {
      emit(currentState.copyWith(isMapView: _isMapView));
    }
  }

  /// Refresh mosque data
  Future<void> refreshMosques() async {
    await initializeMosqueFinder();
  }

  /// Set custom location for mosque search
  Future<void> setCustomLocation(double latitude, double longitude) async {
    try {
      emit(const MosqueFinderLoading(
        progress: 0.5,
        message: 'Searching mosques in new location...',
      ));

      // Update position
      _currentPosition = Position(
        latitude: latitude,
        longitude: longitude,
        timestamp: DateTime.now(),
        accuracy: 0,
        altitude: 0,
        altitudeAccuracy: 0,
        heading: 0,
        headingAccuracy: 0,
        speed: 0,
        speedAccuracy: 0,
      );

      // Get location name
      try {
        final placemarks = await placemarkFromCoordinates(latitude, longitude);
        if (placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          final city = placemark.locality ?? placemark.subAdministrativeArea ?? 'Unknown';
          final state = placemark.administrativeArea ?? 'Unknown';
          _currentLocationName = '$city, $state';
        }
      } catch (e) {
        _currentLocationName = 'Custom Location';
      }

      // Fetch mosques for new location
      await _fetchNearbyMosques();

      emit(MosqueFinderLoaded(
        mosques: _allMosques,
        userLatitude: latitude,
        userLongitude: longitude,
        userLocation: _currentLocationName,
        filters: _currentFilters,
        isMapView: _isMapView,
      ));

    } catch (e) {
      LoggerService.error('❌ MOSQUE: Custom location error: $e');
      emit(MosqueFinderError('Unable to search mosques in this location: ${e.toString()}'));
    }
  }

  /// Show mosque details
  void showMosqueDetails(MosqueModel mosque) {
    emit(MosqueFinderDetails(
      mosque: mosque,
      userLatitude: _currentPosition?.latitude,
      userLongitude: _currentPosition?.longitude,
    ));
  }

  /// Go back to mosque list
  void backToMosqueList() {
    emit(MosqueFinderLoaded(
      mosques: _allMosques,
      userLatitude: _currentPosition?.latitude,
      userLongitude: _currentPosition?.longitude,
      userLocation: _currentLocationName,
      filters: _currentFilters,
      isMapView: _isMapView,
    ));
  }
}
