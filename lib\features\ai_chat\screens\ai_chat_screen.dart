import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import '../cubit/ai_chat_cubit.dart';
import '../cubit/ai_chat_state.dart';
import '../widgets/simple_chat_message.dart';

class AiChatScreen extends StatefulWidget {
  const AiChatScreen({super.key});

  @override
  State<AiChatScreen> createState() => _AiChatScreenState();
}

class _AiChatScreenState extends State<AiChatScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();
  bool _showScrollToBottom = false;

  @override
  void initState() {
    super.initState();

    // Initialize chat
    context.read<AiChatCubit>().initializeChat();

    // Add scroll listener
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    if (!_scrollController.hasClients) return;

    try {
      // Check if we're at the bottom of the list
      final isAtBottom =
          _scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 100;

      if (_showScrollToBottom == isAtBottom) return;

      setState(() {
        _showScrollToBottom = !isAtBottom;
      });
    } catch (e) {
      // Handle any scroll position errors
    }
  }

  double get _scrollProgress {
    if (!_scrollController.hasClients) return 0.0;

    try {
      final maxScroll = _scrollController.position.maxScrollExtent;
      final currentScroll = _scrollController.position.pixels;
      // Normal scroll progress calculation
      return maxScroll > 0 ? (currentScroll / maxScroll).clamp(0.0, 1.0) : 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  void _scrollToBottom() {
    if (!_scrollController.hasClients) return;

    try {
      // Scroll to the bottom of the list
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    } catch (e) {
      // Handle scroll errors
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  Widget _buildSimpleHeader() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          const Spacer(),
          IconButton(
            onPressed: () {
              // TODO: Implement chat history
            },
            icon: Icon(
              Icons.history,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
              size: 20,
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () {
              // TODO: Implement new chat
            },
            icon: Icon(
              Icons.add,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: Theme.of(
                    context,
                  ).colorScheme.outline.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: TextField(
                controller: _messageController,
                maxLines: 6,
                minLines: 1,
                keyboardType: TextInputType.multiline,
                textInputAction: TextInputAction.newline,
                decoration: InputDecoration(
                  hintText: 'Ask Sheikh Noor...',
                  hintStyle: TextStyle(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.5),
                    fontSize: 14,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontSize: 14,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(20),
            ),
            child: IconButton(
              onPressed: () {
                final message = _messageController.text.trim();
                if (message.isNotEmpty) {
                  context.read<AiChatCubit>().sendMessage(message);
                  _messageController.clear();
                }
              },
              icon: const Icon(
                Icons.arrow_upward,
                color: Colors.white,
                size: 18,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: Stack(
          children: [
            Column(
              children: [
                // Simple Header
                _buildSimpleHeader(),

                // Chat Messages
                Expanded(
                  child: BlocBuilder<AiChatCubit, AiChatState>(
                    builder: (context, state) {
                      return _buildChatContent(state);
                    },
                  ),
                ),

                // Simple Input
                _buildSimpleInput(),
              ],
            ),

            // Side scroll indicator
            if (_scrollController.hasClients)
              Positioned(
                right: 4,
                top: 60,
                bottom: 120,
                child: AnimatedBuilder(
                  animation: _scrollController,
                  builder: (context, child) {
                    final progress = _scrollProgress;
                    return Container(
                      width: 4,
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.outline.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.topCenter,
                        heightFactor: progress,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Theme.of(
                              context,
                            ).colorScheme.primary.withValues(alpha: 0.7),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),

            // Scroll to bottom button
            if (_showScrollToBottom)
              Positioned(
                bottom: 100,
                right: 16,
                child: FloatingActionButton.small(
                  onPressed: _scrollToBottom,
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  child: const Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.white,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatContent(AiChatState state) {
    List<dynamic> messages = [];
    String? streamingContent;
    bool isStreaming = false;

    if (state is AiChatLoaded) {
      messages = state.messages;
    } else if (state is AiChatStreaming) {
      messages = state.messages;
      streamingContent = state.streamingContent;
      isStreaming = true;
    } else if (state is AiChatMessageSent) {
      messages = state.messages;
    } else if (state is AiChatError) {
      return Center(
        child: Text(
          'Error: ${state.message}',
          style: TextStyle(color: Theme.of(context).colorScheme.error),
        ),
      );
    }

    if (messages.isEmpty) {
      return const Center(
        child: Text(
          'Start a conversation with Sheikh Noor',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: messages.length,
      itemBuilder: (context, index) {
        final message = messages[index];
        final isLastMessage = index == messages.length - 1;

        return SimpleChatMessage(
          message: message,
          isStreaming: isStreaming && isLastMessage && !message.isUser,
          streamingContent:
              isStreaming && isLastMessage && !message.isUser
                  ? streamingContent
                  : null,
        );
      },
    );
  }
}
