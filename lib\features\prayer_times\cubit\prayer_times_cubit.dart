import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import '../models/prayer_times_model.dart';
import '../services/prayer_times_service.dart';

part 'prayer_times_state.dart';

@injectable
class PrayerTimesCubit extends Cubit<PrayerTimesState> {
  final PrayerTimesService _prayerTimesService;

  PrayerTimesCubit(this._prayerTimesService) : super(PrayerTimesInitial());

  // Load prayer times for current location
  Future<void> loadPrayerTimes({DateTime? date}) async {
    try {
      emit(PrayerTimesLoading());

      final prayerTimes = await _prayerTimesService
          .getPrayerTimesForCurrentLocation(date: date);

      emit(
        PrayerTimesLoaded(
          prayerTimes: prayerTimes,
          nextPrayer: prayerTimes.getNextPrayer(),
          currentPrayer: prayerTimes.getCurrentPrayer(),
        ),
      );
    } catch (e) {
      emit(PrayerTimesError(message: e.toString()));
    }
  }

  // Load prayer times for specific location
  Future<void> loadPrayerTimesForLocation({
    required double latitude,
    required double longitude,
    required String locationName,
    required String country,
    DateTime? date,
    String? calculationMethod,
  }) async {
    try {
      emit(PrayerTimesLoading());

      final prayerTimes = await _prayerTimesService.calculatePrayerTimes(
        latitude: latitude,
        longitude: longitude,
        locationName: locationName,
        country: country,
        date: date,
        calculationMethodName: calculationMethod,
      );

      emit(
        PrayerTimesLoaded(
          prayerTimes: prayerTimes,
          nextPrayer: prayerTimes.getNextPrayer(),
          currentPrayer: prayerTimes.getCurrentPrayer(),
        ),
      );
    } catch (e) {
      emit(PrayerTimesError(message: e.toString()));
    }
  }

  // Load weekly prayer times
  Future<void> loadWeeklyPrayerTimes({
    required double latitude,
    required double longitude,
    required String locationName,
    required String country,
    DateTime? startDate,
  }) async {
    try {
      emit(PrayerTimesLoading());

      final weeklyTimes = await _prayerTimesService.getWeeklyPrayerTimes(
        latitude: latitude,
        longitude: longitude,
        locationName: locationName,
        country: country,
      );

      if (weeklyTimes.isNotEmpty) {
        final todayTimes = weeklyTimes.first;
        emit(
          PrayerTimesLoadedWithWeekly(
            prayerTimes: todayTimes,
            nextPrayer: todayTimes.getNextPrayer(),
            currentPrayer: todayTimes.getCurrentPrayer(),
            weeklyTimes: weeklyTimes,
          ),
        );
      }
    } catch (e) {
      emit(PrayerTimesError(message: e.toString()));
    }
  }

  // Refresh prayer times
  Future<void> refreshPrayerTimes() async {
    if (state is PrayerTimesLoaded) {
      final currentState = state as PrayerTimesLoaded;
      await loadPrayerTimesForLocation(
        latitude: currentState.prayerTimes.latitude,
        longitude: currentState.prayerTimes.longitude,
        locationName: currentState.prayerTimes.locationName,
        country: _extractCountryFromLocation(
          currentState.prayerTimes.locationName,
        ),
      );
    } else {
      await loadPrayerTimes();
    }
  }

  // Get current location and load prayer times with context
  Future<void> getCurrentLocationAndLoadPrayerTimes(
    BuildContext context,
  ) async {
    try {
      emit(PrayerTimesLoading());

      // Clear any saved location to force fresh detection
      await _prayerTimesService.clearSavedLocation();

      // Check if context is still mounted after async operation
      if (context.mounted) {
        final location = await _prayerTimesService
            .getCurrentLocationWithContext(context);

        await loadPrayerTimesForLocation(
          latitude: location.latitude,
          longitude: location.longitude,
          locationName: location.fullAddress,
          country: location.country,
        );
      }
    } catch (e) {
      emit(PrayerTimesError(message: e.toString()));
    }
  }

  // Change calculation method
  Future<void> changeCalculationMethod(String methodName) async {
    if (state is PrayerTimesLoaded) {
      final currentState = state as PrayerTimesLoaded;
      await loadPrayerTimesForLocation(
        latitude: currentState.prayerTimes.latitude,
        longitude: currentState.prayerTimes.longitude,
        locationName: currentState.prayerTimes.locationName,
        country: _extractCountryFromLocation(
          currentState.prayerTimes.locationName,
        ),
        calculationMethod: methodName,
      );
    }
  }

  // Helper method to extract country from location string
  String _extractCountryFromLocation(String locationName) {
    final parts = locationName.split(', ');
    return parts.isNotEmpty ? parts.last : 'Unknown';
  }

  // Get time until next prayer
  Duration? getTimeUntilNextPrayer() {
    if (state is PrayerTimesLoaded) {
      final currentState = state as PrayerTimesLoaded;
      final now = DateTime.now();
      return currentState.nextPrayer.time.difference(now);
    }
    return null;
  }

  // Check if it's prayer time (within 5 minutes)
  bool isPrayerTime() {
    final timeUntil = getTimeUntilNextPrayer();
    if (timeUntil != null) {
      return timeUntil.inMinutes <= 5 && timeUntil.inMinutes >= 0;
    }
    return false;
  }
}
