import 'package:equatable/equatable.dart';
import '../models/qibla_model.dart';

abstract class QiblaState extends Equatable {
  const QiblaState();

  @override
  List<Object?> get props => [];
}

class QiblaInitial extends QiblaState {}

class QiblaLoading extends QiblaState {
  final double progress;
  final String message;

  const QiblaLoading({this.progress = 0.0, this.message = 'Loading...'});

  @override
  List<Object?> get props => [progress, message];
}

class QiblaLoaded extends QiblaState {
  final QiblaModel qiblaData;

  const QiblaLoaded(this.qiblaData);

  @override
  List<Object?> get props => [qiblaData];
}

class QiblaError extends QiblaState {
  final String message;

  const QiblaError(this.message);

  @override
  List<Object?> get props => [message];
}

class QiblaLocationServiceError extends QiblaState {
  final String message;

  const QiblaLocationServiceError(this.message);

  @override
  List<Object?> get props => [message];
}
