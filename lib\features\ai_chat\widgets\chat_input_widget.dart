import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../constants/ai_prompts.dart';

class ChatInputWidget extends StatefulWidget {
  final TextEditingController controller;
  final Function(String) onSendMessage;
  final bool isEnabled;

  const ChatInputWidget({
    super.key,
    required this.controller,
    required this.onSendMessage,
    this.isEnabled = true,
  });

  @override
  State<ChatInputWidget> createState() => _ChatInputWidgetState();
}

class _ChatInputWidgetState extends State<ChatInputWidget>
    with TickerProviderStateMixin {
  late AnimationController _sendButtonController;
  late Animation<double> _sendButtonAnimation;
  bool _showQuickReplies = true;

  @override
  void initState() {
    super.initState();

    _sendButtonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _sendButtonAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _sendButtonController, curve: Curves.elasticOut),
    );

    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _sendButtonController.dispose();
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onTextChanged() {
    if (widget.controller.text.isNotEmpty) {
      _sendButtonController.forward();
      if (_showQuickReplies) {
        setState(() {
          _showQuickReplies = false;
        });
      }
    } else {
      _sendButtonController.reverse();
      if (!_showQuickReplies) {
        setState(() {
          _showQuickReplies = true;
        });
      }
    }
  }

  void _sendMessage() {
    final message = widget.controller.text.trim();
    if (message.isNotEmpty && widget.isEnabled) {
      widget.onSendMessage(message);
      widget.controller.clear();
      _sendButtonController.reverse();
      setState(() {
        _showQuickReplies = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Quick Reply Suggestions
          if (_showQuickReplies) _buildQuickReplies(),

          // Input Row
          Row(
            children: [
              // Text Input
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(25.r),
                    border: Border.all(
                      color: Theme.of(
                        context,
                      ).colorScheme.outline.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: TextField(
                    controller: widget.controller,
                    enabled: widget.isEnabled,
                    maxLines: 6,
                    minLines: 1,
                    textInputAction: TextInputAction.newline,
                    keyboardType: TextInputType.multiline,
                    onSubmitted: (value) {
                      // Only send on Enter without Shift
                      _sendMessage();
                    },
                    decoration: InputDecoration(
                      hintText:
                          'Ask Sheikh Noor about Islam...\nPress Shift+Enter for new line',
                      hintStyle: AppTextStyles.bodyMedium.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.5),
                        fontSize: 14.sp,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16.w,
                        vertical: 14.h,
                      ),
                    ),
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontSize: 15.sp,
                      height: 1.4,
                    ),
                  ),
                ),
              ),

              SizedBox(width: 12.w),

              // Send Button
              ScaleTransition(
                scale: _sendButtonAnimation,
                child: Container(
                  width: 48.w,
                  height: 48.w,
                  decoration: BoxDecoration(
                    gradient:
                        widget.controller.text.isNotEmpty && widget.isEnabled
                            ? LinearGradient(colors: AppColors.primaryGradient)
                            : LinearGradient(
                              colors: [
                                Theme.of(
                                  context,
                                ).colorScheme.outline.withValues(alpha: 0.3),
                                Theme.of(
                                  context,
                                ).colorScheme.outline.withValues(alpha: 0.2),
                              ],
                            ),
                    borderRadius: BorderRadius.circular(24.r),
                    boxShadow:
                        widget.controller.text.isNotEmpty && widget.isEnabled
                            ? [
                              BoxShadow(
                                color: AppColors.primaryGreen.withValues(
                                  alpha: 0.3,
                                ),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ]
                            : null,
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(24.r),
                      onTap: widget.isEnabled ? _sendMessage : null,
                      child: Icon(
                        Icons.send_rounded,
                        color:
                            widget.controller.text.isNotEmpty &&
                                    widget.isEnabled
                                ? Colors.white
                                : Theme.of(
                                  context,
                                ).colorScheme.onSurface.withValues(alpha: 0.4),
                        size: 20.sp,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickReplies() {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      height: 40.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: AiPrompts.conversationStarters.length,
        itemBuilder: (context, index) {
          final starter = AiPrompts.conversationStarters[index];
          return Container(
            margin: EdgeInsets.only(right: 8.w),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(20.r),
                onTap:
                    widget.isEnabled
                        ? () {
                          widget.controller.text = starter;
                          _sendMessage();
                        }
                        : null,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 8.h,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20.r),
                    border: Border.all(
                      color: Theme.of(
                        context,
                      ).colorScheme.primary.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    starter,
                    style: AppTextStyles.labelMedium.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
