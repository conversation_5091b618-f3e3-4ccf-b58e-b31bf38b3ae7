import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../features/prayer_times/services/prayer_times_service.dart';
import '../../features/prayer_times/services/location_permission_service.dart';
import '../../features/prayer_times/cubit/prayer_times_cubit.dart';

final GetIt getIt = GetIt.instance;

// Manual registration for packages that don't support injectable
Future<void> setupDependencies() async {
  // Register shared preferences
  final prefs = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(prefs);

  // Register location permission service
  getIt.registerLazySingleton<LocationPermissionService>(
    () => LocationPermissionService(),
  );

  // Register prayer times service
  getIt.registerLazySingleton<PrayerTimesService>(
    () => PrayerTimesService(getIt<SharedPreferences>()),
  );

  // Register prayer times cubit
  getIt.registerFactory<PrayerTimesCubit>(
    () => PrayerTimesCubit(getIt<PrayerTimesService>()),
  );
}
