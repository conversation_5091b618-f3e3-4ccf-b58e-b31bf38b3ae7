import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../constants/ai_prompts.dart';
import 'logger_service.dart';

class AiService {
  static final AiService _instance = AiService._internal();
  factory AiService() => _instance;
  AiService._internal();

  late final Dio _dio;
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
  
  // Initialize the service
  Future<void> initialize() async {
    _dio = Dio(BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 60),
      headers: {
        'Content-Type': 'application/json',
      },
    ));

    // Add interceptors for logging
    _dio.interceptors.add(LogInterceptor(
      requestBody: false, // Don't log request body for privacy
      responseBody: false, // Don't log response body for privacy
      logPrint: (obj) => LoggerService.ai('🤖 AI API: $obj'),
    ));

    LoggerService.ai('🤖 AI: Service initialized successfully');
  }

  // Get API key from environment
  String get _apiKey {
    final key = dotenv.env['GEMINI_API_KEY'];
    if (key == null || key.isEmpty) {
      throw Exception('GEMINI_API_KEY not found in environment variables');
    }
    return key;
  }

  // Get AI model from environment
  String get _model {
    return dotenv.env['AI_MODEL'] ?? 'gemini-2.5-flash';
  }

  // Send message and get streaming response
  Stream<String> sendMessageStream(String message, {List<ChatMessage>? history}) async* {
    try {
      LoggerService.ai('🤖 AI: Sending message stream request');
      
      // Prepare the conversation history
      final contents = _buildConversationContents(message, history);
      
      // Prepare request body
      final requestBody = {
        'contents': contents,
        'generationConfig': {
          'temperature': 0.7,
          'topK': 40,
          'topP': 0.95,
          'maxOutputTokens': 2048,
        },
        'safetySettings': [
          {
            'category': 'HARM_CATEGORY_HARASSMENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_HATE_SPEECH',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_DANGEROUS_CONTENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          }
        ]
      };

      // Make streaming request
      final response = await _dio.post(
        '/models/$_model:streamGenerateContent?key=$_apiKey',
        data: requestBody,
        options: Options(
          responseType: ResponseType.stream,
        ),
      );

      // Process streaming response
      await for (final chunk in _processStreamResponse(response.data)) {
        yield chunk;
      }

      LoggerService.ai('🤖 AI: Stream completed successfully');
    } catch (e) {
      LoggerService.error('❌ AI: Stream error: $e');
      yield AiPrompts.errorMessage;
    }
  }

  // Send message and get complete response
  Future<String> sendMessage(String message, {List<ChatMessage>? history}) async {
    try {
      LoggerService.ai('🤖 AI: Sending message request');
      
      // Prepare the conversation history
      final contents = _buildConversationContents(message, history);
      
      // Prepare request body
      final requestBody = {
        'contents': contents,
        'generationConfig': {
          'temperature': 0.7,
          'topK': 40,
          'topP': 0.95,
          'maxOutputTokens': 2048,
        },
      };

      // Make request
      final response = await _dio.post(
        '/models/$_model:generateContent?key=$_apiKey',
        data: requestBody,
      );

      // Extract response text
      final responseData = response.data;
      if (responseData['candidates'] != null && 
          responseData['candidates'].isNotEmpty &&
          responseData['candidates'][0]['content'] != null &&
          responseData['candidates'][0]['content']['parts'] != null &&
          responseData['candidates'][0]['content']['parts'].isNotEmpty) {
        
        final text = responseData['candidates'][0]['content']['parts'][0]['text'] as String;
        LoggerService.ai('🤖 AI: Response received successfully');
        return text;
      } else {
        throw Exception('Invalid response format from AI service');
      }
    } catch (e) {
      LoggerService.error('❌ AI: Request error: $e');
      return AiPrompts.errorMessage;
    }
  }

  // Build conversation contents for API
  List<Map<String, dynamic>> _buildConversationContents(String message, List<ChatMessage>? history) {
    final contents = <Map<String, dynamic>>[];
    
    // Add system prompt as first message
    contents.add({
      'role': 'user',
      'parts': [{'text': AiPrompts.islamicScholarSystemPrompt}]
    });
    
    contents.add({
      'role': 'model',
      'parts': [{'text': 'Assalamu Alaikum wa Rahmatullahi wa Barakatuh! I am Sheikh AI, your Islamic knowledge companion. How may I assist you in your Islamic journey today?'}]
    });

    // Add conversation history
    if (history != null) {
      for (final msg in history) {
        contents.add({
          'role': msg.isUser ? 'user' : 'model',
          'parts': [{'text': msg.content}]
        });
      }
    }

    // Add current message
    contents.add({
      'role': 'user',
      'parts': [{'text': message}]
    });

    return contents;
  }

  // Process streaming response
  Stream<String> _processStreamResponse(Stream<List<int>> stream) async* {
    await for (final chunk in stream) {
      final chunkString = utf8.decode(chunk);
      final lines = chunkString.split('\n');
      
      for (final line in lines) {
        if (line.trim().isEmpty) continue;
        
        try {
          // Remove "data: " prefix if present
          final jsonLine = line.startsWith('data: ') ? line.substring(6) : line;
          if (jsonLine.trim().isEmpty) continue;
          
          final data = jsonDecode(jsonLine);
          
          if (data['candidates'] != null && 
              data['candidates'].isNotEmpty &&
              data['candidates'][0]['content'] != null &&
              data['candidates'][0]['content']['parts'] != null &&
              data['candidates'][0]['content']['parts'].isNotEmpty) {
            
            final text = data['candidates'][0]['content']['parts'][0]['text'] as String?;
            if (text != null && text.isNotEmpty) {
              yield text;
            }
          }
        } catch (e) {
          // Skip invalid JSON lines
          continue;
        }
      }
    }
  }
}

// Chat message model
class ChatMessage {
  final String content;
  final bool isUser;
  final DateTime timestamp;
  final String? id;

  ChatMessage({
    required this.content,
    required this.isUser,
    required this.timestamp,
    this.id,
  });

  Map<String, dynamic> toJson() => {
    'content': content,
    'isUser': isUser,
    'timestamp': timestamp.toIso8601String(),
    'id': id,
  };

  factory ChatMessage.fromJson(Map<String, dynamic> json) => ChatMessage(
    content: json['content'],
    isUser: json['isUser'],
    timestamp: DateTime.parse(json['timestamp']),
    id: json['id'],
  );
}
