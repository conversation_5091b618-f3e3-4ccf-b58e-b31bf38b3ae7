import 'package:logger/logger.dart';

class LoggerService {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
    ),
  );

  // Debug level - for development debugging
  static void debug(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.d(message, error: error, stackTrace: stackTrace);
  }

  // Info level - for general information
  static void info(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  // Warning level - for potential issues
  static void warning(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  // Error level - for errors
  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  // Fatal level - for critical errors
  static void fatal(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.f(message, error: error, stackTrace: stackTrace);
  }

  // Location specific logging
  static void location(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) {
    _logger.i('📍 LOCATION: $message', error: error, stackTrace: stackTrace);
  }

  // Prayer times specific logging
  static void prayerTimes(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) {
    _logger.i('🕌 PRAYER: $message', error: error, stackTrace: stackTrace);
  }

  // Cache specific logging
  static void cache(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i('💾 CACHE: $message', error: error, stackTrace: stackTrace);
  }

  // Performance logging
  static void performance(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) {
    _logger.i('⚡ PERF: $message', error: error, stackTrace: stackTrace);
  }

  // Qibla finder specific logging
  static void qibla(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i('🧭 QIBLA: $message', error: error, stackTrace: stackTrace);
  }

  // Mosque finder specific logging
  static void mosque(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i('🕌 MOSQUE: $message', error: error, stackTrace: stackTrace);
  }

  // Quran specific logging
  static void quran(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i('📖 QURAN: $message', error: error, stackTrace: stackTrace);
  }

  // Duas specific logging
  static void duas(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i('🤲 DUAS: $message', error: error, stackTrace: stackTrace);
  }
}
