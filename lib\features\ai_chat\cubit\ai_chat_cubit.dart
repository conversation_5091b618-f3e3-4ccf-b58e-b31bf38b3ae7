import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../services/ai_service.dart';
import '../../../core/services/logger_service.dart';
import '../constants/ai_prompts.dart';
import '../models/chat_message.dart';
import 'ai_chat_state.dart';

class AiChatCubit extends Cubit<AiChatState> {
  final AiService _aiService = AiService();
  final List<ChatMessage> _messages = [];
  StreamSubscription? _streamSubscription;
  static const String _messagesKey = 'ai_chat_messages';

  AiChatCubit() : super(AiChatInitial());

  /// Initialize the AI chat
  Future<void> initializeChat() async {
    try {
      emit(const AiChatLoading(message: 'Initializing Sheikh AI...'));
      LoggerService.info('🤖 CHAT: Initializing AI chat...');

      // Initialize AI service
      await _aiService.initialize();

      // Load previous messages
      await _loadMessages();

      // Check if this is first time
      final isFirstTime = _messages.isEmpty;

      if (isFirstTime) {
        // Add welcome message for first time users
        final welcomeMessage = ChatMessage.system(
          content: AiPrompts.welcomeMessage,
          id: 'welcome_${DateTime.now().millisecondsSinceEpoch}',
        );

        _messages.add(welcomeMessage);
        await _saveMessages();
      }

      emit(
        AiChatLoaded(messages: List.from(_messages), isFirstTime: isFirstTime),
      );

      LoggerService.info('🤖 CHAT: Chat initialized successfully');
    } catch (e) {
      LoggerService.error('❌ CHAT: Initialization error: $e');
      emit(
        AiChatError(message: 'Failed to initialize AI chat: ${e.toString()}'),
      );
    }
  }

  /// Send a message to AI
  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty) return;

    try {
      LoggerService.info(
        '🤖 CHAT: Sending message: ${content.substring(0, content.length > 50 ? 50 : content.length)}...',
      );

      // Add user message
      final userMessage = ChatMessage.user(
        content: content.trim(),
        id: 'user_${DateTime.now().millisecondsSinceEpoch}',
      );

      _messages.add(userMessage);

      // Create placeholder AI message for streaming
      final aiMessage = ChatMessage.ai(
        content: '',
        id: 'ai_${DateTime.now().millisecondsSinceEpoch}',
      );

      _messages.add(aiMessage);
      await _saveMessages();

      // Start streaming response immediately
      await _streamAiResponse(userMessage);
    } catch (e) {
      LoggerService.error('❌ CHAT: Send message error: $e');
      emit(
        AiChatError(
          message: 'Failed to send message: ${e.toString()}',
          messages: List.from(_messages),
        ),
      );
    }
  }

  /// Stream AI response
  Future<void> _streamAiResponse(ChatMessage userMessage) async {
    try {
      String streamingContent = '';

      // Send message to AI service (it handles conversation history internally)
      _streamSubscription = _aiService
          .sendMessageStream(userMessage.content)
          .listen(
            (chunk) {
              streamingContent =
                  chunk; // Use the accumulated content from service
              emit(
                AiChatStreaming(
                  messages: List.from(_messages),
                  streamingContent: streamingContent,
                  userMessage: userMessage,
                ),
              );
            },
            onDone: () async {
              // Update the last AI message with final content
              if (_messages.isNotEmpty && !_messages.last.isUser) {
                _messages.last = ChatMessage.ai(
                  content: streamingContent.trim(),
                  id: _messages.last.id,
                );
              }

              await _saveMessages();

              emit(
                AiChatLoaded(messages: List.from(_messages), isTyping: false),
              );

              LoggerService.info('🤖 CHAT: AI response completed');
            },
            onError: (error) {
              LoggerService.error('❌ CHAT: Stream error: $error');
              emit(
                AiChatError(
                  message: 'Failed to get AI response: ${error.toString()}',
                  messages: List.from(_messages),
                ),
              );
            },
          );
    } catch (e) {
      LoggerService.error('❌ CHAT: Stream setup error: $e');
      emit(
        AiChatError(
          message: 'Failed to setup AI response: ${e.toString()}',
          messages: List.from(_messages),
        ),
      );
    }
  }

  /// Clear chat history
  Future<void> clearChat() async {
    try {
      LoggerService.info('🤖 CHAT: Clearing chat history');

      _messages.clear();
      await _saveMessages();

      // Reinitialize with welcome message
      await initializeChat();

      LoggerService.info('🤖 CHAT: Chat history cleared');
    } catch (e) {
      LoggerService.error('❌ CHAT: Clear chat error: $e');
      emit(AiChatError(message: 'Failed to clear chat: ${e.toString()}'));
    }
  }

  /// Load messages from storage
  Future<void> _loadMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = prefs.getString(_messagesKey);

      if (messagesJson != null) {
        final messagesList = jsonDecode(messagesJson) as List;
        _messages.clear();
        _messages.addAll(
          messagesList.map((json) => ChatMessage.fromJson(json)).toList(),
        );
        LoggerService.info(
          '🤖 CHAT: Loaded ${_messages.length} messages from storage',
        );
      }
    } catch (e) {
      LoggerService.error('❌ CHAT: Load messages error: $e');
    }
  }

  /// Save messages to storage
  Future<void> _saveMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = jsonEncode(
        _messages.map((msg) => msg.toJson()).toList(),
      );
      await prefs.setString(_messagesKey, messagesJson);
    } catch (e) {
      LoggerService.error('❌ CHAT: Save messages error: $e');
    }
  }

  /// Get message count
  int get messageCount => _messages.length;

  /// Check if chat is empty
  bool get isEmpty => _messages.isEmpty;

  @override
  Future<void> close() {
    _streamSubscription?.cancel();
    return super.close();
  }
}
