import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../presentation/widgets/gradient_background.dart';
import '../cubit/mosque_finder_cubit.dart';
import '../cubit/mosque_finder_state.dart';
import '../models/mosque_model.dart';
import '../widgets/mosque_loading_widget.dart';
import '../widgets/mosque_list_view.dart';
import '../widgets/mosque_map_view.dart';
import '../widgets/mosque_search_bar.dart';
import '../widgets/mosque_filter_sheet.dart';

class MosqueFinderScreen extends StatefulWidget {
  const MosqueFinderScreen({super.key});

  @override
  State<MosqueFinderScreen> createState() => _MosqueFinderScreenState();
}

class _MosqueFinderScreenState extends State<MosqueFinderScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    // Initialize mosque finder
    context.read<MosqueFinderCubit>().initializeMosqueFinder();
    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        final navigator = Navigator.of(context);
        final shouldExit = await _showExitConfirmation(context);
        if (shouldExit && mounted) {
          navigator.pop();
        }
      },
      child: Scaffold(
        body: GradientBackground(
          child: SafeArea(
            child: Column(
              children: [
                // Header
                _buildHeader(),

                // Content
                Expanded(
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: BlocBuilder<MosqueFinderCubit, MosqueFinderState>(
                      builder: (context, state) {
                        if (state is MosqueFinderLoading) {
                          return MosqueLoadingWidget(
                            progress: state.progress,
                            message: state.message,
                          );
                        } else if (state is MosqueFinderLocationServiceError) {
                          return _buildLocationServiceErrorWidget(
                            state.message,
                          );
                        } else if (state is MosqueFinderError) {
                          return _buildErrorWidget(state.message);
                        } else if (state is MosqueFinderLoaded) {
                          return _buildMosqueContent(state);
                        } else if (state is MosqueFinderSearchResults) {
                          return _buildSearchResults(state);
                        } else if (state is MosqueFinderSearching) {
                          return MosqueLoadingWidget(
                            progress: state.progress,
                            message: 'Searching for "${state.query}"...',
                          );
                        } else if (state is MosqueFinderDetails) {
                          return _buildMosqueDetails(state);
                        }
                        return const MosqueLoadingWidget();
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      child: Row(
        children: [
          // Back button
          Container(
            decoration: BoxDecoration(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.2)
                      : Colors.black.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: IconButton(
              onPressed: () async {
                final shouldExit = await _showExitConfirmation(context);
                if (shouldExit && mounted) {
                  Navigator.pop(context);
                }
              },
              icon: Icon(
                Icons.arrow_back_ios_new,
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                size: 20.sp,
              ),
            ),
          ),

          SizedBox(width: 16.w),

          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Mosque Finder',
                  style: AppTextStyles.headlineMedium.copyWith(
                    color:
                        Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black87,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                Text(
                  'Find nearby mosques',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color:
                        Theme.of(context).brightness == Brightness.dark
                            ? Colors.white.withValues(alpha: 0.8)
                            : Colors.black.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),

          // View toggle and filter buttons
          BlocBuilder<MosqueFinderCubit, MosqueFinderState>(
            builder: (context, state) {
              if (state is MosqueFinderLoaded) {
                return Row(
                  children: [
                    // Filter button
                    Container(
                      decoration: BoxDecoration(
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withValues(alpha: 0.2)
                                : Colors.black.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: IconButton(
                        onPressed:
                            () => _showFilterSheet(context, state.filters),
                        icon: Icon(
                          Icons.tune,
                          color:
                              Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white
                                  : Colors.black87,
                          size: 20.sp,
                        ),
                      ),
                    ),

                    SizedBox(width: 8.w),

                    // View toggle button
                    Container(
                      decoration: BoxDecoration(
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withValues(alpha: 0.2)
                                : Colors.black.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: IconButton(
                        onPressed:
                            () =>
                                context.read<MosqueFinderCubit>().toggleView(),
                        icon: Icon(
                          state.isMapView ? Icons.list : Icons.map,
                          color:
                              Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white
                                  : Colors.black87,
                          size: 20.sp,
                        ),
                      ),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMosqueContent(MosqueFinderLoaded state) {
    return Column(
      children: [
        // Search bar
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: MosqueSearchBar(
            onSearch:
                (query) =>
                    context.read<MosqueFinderCubit>().searchMosques(query),
            onLocationTap: () => _showLocationPicker(context),
            currentLocation: state.userLocation ?? 'Current Location',
          ),
        ),

        SizedBox(height: 16.h),

        // Mosque count and filters info
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Row(
            children: [
              Text(
                '${state.mosques.length} mosques found',
                style: AppTextStyles.bodyMedium.copyWith(
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.8)
                          : Colors.black.withValues(alpha: 0.7),
                ),
              ),
              if (state.filters.maxDistance != 10.0) ...[
                Text(
                  ' • Within ${state.filters.maxDistance.toStringAsFixed(0)}km',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.primaryGreen,
                  ),
                ),
              ],
            ],
          ),
        ),

        SizedBox(height: 16.h),

        // Content view (map or list)
        Expanded(
          child:
              state.isMapView
                  ? MosqueMapView(
                    mosques: state.mosques,
                    userLatitude: state.userLatitude,
                    userLongitude: state.userLongitude,
                    onMosqueTap:
                        (mosque) => context
                            .read<MosqueFinderCubit>()
                            .showMosqueDetails(mosque),
                  )
                  : MosqueListView(
                    mosques: state.mosques,
                    userLatitude: state.userLatitude,
                    userLongitude: state.userLongitude,
                    onMosqueTap:
                        (mosque) => context
                            .read<MosqueFinderCubit>()
                            .showMosqueDetails(mosque),
                  ),
        ),
      ],
    );
  }

  Widget _buildSearchResults(MosqueFinderSearchResults state) {
    return Column(
      children: [
        // Search results header
        Padding(
          padding: EdgeInsets.all(20.w),
          child: Row(
            children: [
              IconButton(
                onPressed:
                    () => context.read<MosqueFinderCubit>().backToMosqueList(),
                icon: Icon(
                  Icons.arrow_back,
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.black87,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Search Results',
                      style: AppTextStyles.titleLarge.copyWith(
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.white
                                : Colors.black87,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      '${state.results.length} results for "${state.query}"',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withValues(alpha: 0.8)
                                : Colors.black.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Search results list
        Expanded(
          child: MosqueListView(
            mosques: state.results,
            userLatitude: state.userLatitude,
            userLongitude: state.userLongitude,
            onMosqueTap:
                (mosque) =>
                    context.read<MosqueFinderCubit>().showMosqueDetails(mosque),
          ),
        ),
      ],
    );
  }

  Widget _buildMosqueDetails(MosqueFinderDetails state) {
    // This will be implemented in the next file
    return const Center(child: Text('Mosque Details - Coming Next!'));
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64.sp,
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.7)
                      : Colors.black.withValues(alpha: 0.7),
            ),

            SizedBox(height: 16.h),

            Text(
              'Unable to Find Mosques',
              style: AppTextStyles.titleLarge.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 8.h),

            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.8)
                        : Colors.black.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 24.h),

            ElevatedButton(
              onPressed:
                  () => context.read<MosqueFinderCubit>().refreshMosques(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGreen,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Text(
                'Try Again',
                style: AppTextStyles.titleMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationServiceErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_off,
              size: 64.sp,
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.7)
                      : Colors.black.withValues(alpha: 0.7),
            ),

            SizedBox(height: 16.h),

            Text(
              'Location Access Needed',
              style: AppTextStyles.titleLarge.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 8.h),

            Text(
              message.contains('permission')
                  ? 'We need location permission to find nearby mosques.'
                  : 'Please turn on GPS/Location services to find nearby mosques.',
              style: AppTextStyles.bodyMedium.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.8)
                        : Colors.black.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 24.h),

            // Smart action button based on error type
            ElevatedButton.icon(
              onPressed: () async {
                if (message.contains('permission')) {
                  // For permission issues, try to request permission again
                  context.read<MosqueFinderCubit>().initializeMosqueFinder();
                } else {
                  // For GPS/location service issues, open settings
                  await Geolocator.openLocationSettings();
                }
              },
              icon: Icon(
                message.contains('permission')
                    ? Icons.security
                    : Icons.settings,
                size: 20.sp,
              ),
              label: Text(
                message.contains('permission')
                    ? 'Allow Location Access'
                    : 'Open Location Settings',
                style: AppTextStyles.titleMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGreen,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
            ),

            SizedBox(height: 12.h),

            // Try Again button
            TextButton(
              onPressed:
                  () =>
                      context
                          .read<MosqueFinderCubit>()
                          .initializeMosqueFinder(),
              child: Text(
                'Try Again',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.primaryGreen,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showFilterSheet(
    BuildContext context,
    MosqueSearchFilters currentFilters,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => MosqueFilterSheet(
            currentFilters: currentFilters,
            onFiltersChanged:
                (filters) =>
                    context.read<MosqueFinderCubit>().updateFilters(filters),
          ),
    );
  }

  void _showLocationPicker(BuildContext context) {
    // This will be implemented later for custom location selection
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Custom location picker coming soon!')),
    );
  }

  /// Show exit confirmation dialog
  Future<bool> _showExitConfirmation(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext dialogContext) {
            return AlertDialog(
              backgroundColor:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[900]
                      : Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16.r),
              ),
              title: Row(
                children: [
                  Icon(
                    Icons.exit_to_app,
                    color: AppColors.primaryGreen,
                    size: 24.sp,
                  ),
                  SizedBox(width: 12.w),
                  Text(
                    'Exit Mosque Finder?',
                    style: AppTextStyles.titleMedium.copyWith(
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black87,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              content: Text(
                'Are you sure you want to exit? Your current search and location will be lost.',
                style: AppTextStyles.bodyMedium.copyWith(
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.8)
                          : Colors.black.withValues(alpha: 0.7),
                ),
              ),
              actions: [
                // Cancel button
                TextButton(
                  onPressed: () => Navigator.of(dialogContext).pop(false),
                  child: Text(
                    'Cancel',
                    style: AppTextStyles.titleSmall.copyWith(
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.8)
                              : Colors.black.withValues(alpha: 0.7),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),

                // Exit button
                ElevatedButton(
                  onPressed: () => Navigator.of(dialogContext).pop(true),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryGreen,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(
                      horizontal: 20.w,
                      vertical: 8.h,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                  child: Text(
                    'Exit',
                    style: AppTextStyles.titleSmall.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            );
          },
        ) ??
        false;
  }
}
