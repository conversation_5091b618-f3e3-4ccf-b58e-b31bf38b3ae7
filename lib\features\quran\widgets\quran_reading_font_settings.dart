import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/quran_models.dart';

class QuranReadingFontSettings extends StatefulWidget {
  final QuranReadingPreferences preferences;
  final Function(QuranReadingPreferences) onPreferencesChanged;

  const QuranReadingFontSettings({
    super.key,
    required this.preferences,
    required this.onPreferencesChanged,
  });

  @override
  State<QuranReadingFontSettings> createState() =>
      _QuranReadingFontSettingsState();
}

class _QuranReadingFontSettingsState extends State<QuranReadingFontSettings>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  late QuranReadingPreferences _currentPreferences;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _currentPreferences = widget.preferences;

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  void _toggleSettings() {
    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (_isExpanded) {
      _slideController.forward();
    } else {
      _slideController.reverse();
    }
  }

  void _updatePreferences(QuranReadingPreferences newPreferences) {
    setState(() {
      _currentPreferences = newPreferences;
    });
    widget.onPreferencesChanged(newPreferences);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Settings button - positioned higher to avoid hiding Quran numbers
        Positioned(top: 8.h, right: 16.w, child: _buildSettingsButton()),

        // Settings panel
        if (_isExpanded)
          Positioned(
            top: 65.h,
            right: 16.w,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildSettingsPanel(),
            ),
          ),
      ],
    );
  }

  Widget _buildSettingsButton() {
    return Container(
      width: 48.w,
      height: 48.w,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors:
              _isExpanded
                  ? [AppColors.primaryGreen, AppColors.secondaryGold]
                  : [
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.black.withValues(alpha: 0.9)
                        : Colors.white.withValues(alpha: 0.98),
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey[900]!.withValues(alpha: 0.9)
                        : Colors.grey[50]!.withValues(alpha: 0.98),
                  ],
        ),
        borderRadius: BorderRadius.circular(26.r),
        boxShadow: [
          BoxShadow(
            color:
                _isExpanded
                    ? AppColors.primaryGreen.withValues(alpha: 0.3)
                    : Colors.black.withValues(alpha: 0.15),
            blurRadius: _isExpanded ? 20.r : 15.r,
            offset: Offset(0, _isExpanded ? 8.h : 5.h),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 25.r,
            offset: Offset(0, 10.h),
          ),
        ],
        border: Border.all(
          color:
              _isExpanded
                  ? Colors.white.withValues(alpha: 0.3)
                  : AppColors.primaryGreen.withValues(alpha: 0.2),
          width: 1.5,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _toggleSettings,
          borderRadius: BorderRadius.circular(26.r),
          child: AnimatedRotation(
            turns: _isExpanded ? 0.125 : 0.0,
            duration: const Duration(milliseconds: 300),
            child: Icon(
              Icons.tune,
              color:
                  _isExpanded
                      ? Colors.white
                      : Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.9)
                      : AppColors.primaryGreen,
              size: 26.sp,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsPanel() {
    return Container(
      width: 280.w,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.95)
                : Colors.white.withValues(alpha: 0.98),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 25.r,
            offset: Offset(0, 10.h),
          ),
        ],
        border: Border.all(
          color: AppColors.primaryGreen.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.text_fields,
                color: AppColors.primaryGreen,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'Reading Settings',
                style: AppTextStyles.titleMedium.copyWith(
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.black87,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: _toggleSettings,
                icon: Icon(
                  Icons.close,
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.7)
                          : Colors.black.withValues(alpha: 0.7),
                  size: 18.sp,
                ),
                padding: EdgeInsets.zero,
                constraints: BoxConstraints(minWidth: 24.w, minHeight: 24.w),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Arabic Font Size
          _buildFontSizeSlider(),

          SizedBox(height: 16.h),

          // Line Spacing
          _buildLineSpacingSlider(),

          SizedBox(height: 16.h),

          // Show Translation Toggle
          _buildTranslationToggle(),
        ],
      ),
    );
  }

  Widget _buildFontSizeSlider() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Font Size',
              style: AppTextStyles.bodyMedium.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                fontWeight: FontWeight.w500,
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
              decoration: BoxDecoration(
                color: AppColors.primaryGreen.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                '${_currentPreferences.arabicFontSize.toInt()}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.primaryGreen,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),

        SizedBox(height: 8.h),

        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: AppColors.primaryGreen,
            inactiveTrackColor: AppColors.primaryGreen.withValues(alpha: 0.3),
            thumbColor: AppColors.primaryGreen,
            overlayColor: AppColors.primaryGreen.withValues(alpha: 0.2),
            trackHeight: 3.h,
            thumbShape: RoundSliderThumbShape(enabledThumbRadius: 8.r),
          ),
          child: Slider(
            value: _currentPreferences.arabicFontSize,
            min: 16.0,
            max: 32.0,
            divisions: 16,
            onChanged: (value) {
              _updatePreferences(
                _currentPreferences.copyWith(arabicFontSize: value),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLineSpacingSlider() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Line Spacing',
              style: AppTextStyles.bodyMedium.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                fontWeight: FontWeight.w500,
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
              decoration: BoxDecoration(
                color: AppColors.primaryGreen.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                _currentPreferences.lineSpacing.toStringAsFixed(1),
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.primaryGreen,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),

        SizedBox(height: 8.h),

        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: AppColors.primaryGreen,
            inactiveTrackColor: AppColors.primaryGreen.withValues(alpha: 0.3),
            thumbColor: AppColors.primaryGreen,
            overlayColor: AppColors.primaryGreen.withValues(alpha: 0.2),
            trackHeight: 3.h,
            thumbShape: RoundSliderThumbShape(enabledThumbRadius: 8.r),
          ),
          child: Slider(
            value: _currentPreferences.lineSpacing,
            min: 1.2,
            max: 2.5,
            divisions: 13,
            onChanged: (value) {
              _updatePreferences(
                _currentPreferences.copyWith(lineSpacing: value),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTranslationToggle() {
    return Row(
      children: [
        Expanded(
          child: Text(
            'Show Translation',
            style: AppTextStyles.bodyMedium.copyWith(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black87,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),

        Switch(
          value: _currentPreferences.showTranslation,
          onChanged: (value) {
            _updatePreferences(
              _currentPreferences.copyWith(showTranslation: value),
            );
          },
          activeColor: AppColors.primaryGreen,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      ],
    );
  }
}
