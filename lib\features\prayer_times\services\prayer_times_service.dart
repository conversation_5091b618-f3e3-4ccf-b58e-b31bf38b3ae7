import 'package:flutter/material.dart';
import 'package:adhan/adhan.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:injectable/injectable.dart';
import 'dart:convert';
import '../models/prayer_times_model.dart';
import '../../../core/services/logger_service.dart';

@injectable
class PrayerTimesService {
  final SharedPreferences _prefs;

  PrayerTimesService(this._prefs);

  // Get current location (exact copy of working old code approach)
  Future<LocationModel> getCurrentLocationWithContext(
    BuildContext context,
  ) async {
    try {
      // Direct location detection like your working code
      final position = await _determinePosition();

      return await _processLocationPosition(
        position,
        'Unknown, Unknown, Unknown',
      );
    } catch (e) {
      rethrow;
    }
  }

  // Enhanced location detection with native permission dialogs
  Future<Position> _determinePosition() async {
    try {
      LoggerService.location('Checking location services...');
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      LoggerService.location('Location services enabled: $serviceEnabled');

      // Always check permissions first, even if services are disabled
      LoggerService.location('Checking location permissions...');
      LocationPermission permission = await Geolocator.checkPermission();
      LoggerService.location('Current permission: $permission');

      // Request permission if denied (this shows native dialog)
      if (permission == LocationPermission.denied) {
        LoggerService.location('Requesting location permission...');
        permission = await Geolocator.requestPermission();
        LoggerService.location('Permission after request: $permission');

        if (permission == LocationPermission.denied) {
          throw 'Location permission denied. Please allow location access to get prayer times.';
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw 'Location permission permanently denied. Please enable location access in app settings.';
      }

      // Now check if location services are enabled (after permission is granted)
      if (!serviceEnabled) {
        // Re-check in case user enabled it during permission request
        serviceEnabled = await Geolocator.isLocationServiceEnabled();
        if (!serviceEnabled) {
          throw 'Location services disabled. Please enable GPS/Location in your device settings.';
        }
      }

      LoggerService.location('Getting current position...');
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy:
              LocationAccuracy
                  .medium, // Changed from high to medium for faster response
          timeLimit: Duration(seconds: 30), // Increased timeout
        ),
      ).timeout(
        const Duration(seconds: 25), // Additional timeout wrapper
        onTimeout: () async {
          LoggerService.warning(
            'High accuracy timeout, trying with lower accuracy...',
          );
          // Fallback to lower accuracy if high accuracy times out
          return await Geolocator.getCurrentPosition(
            locationSettings: const LocationSettings(
              accuracy: LocationAccuracy.low,
              timeLimit: Duration(seconds: 15),
            ),
          );
        },
      );
      LoggerService.location(
        'Location found: ${position.latitude}, ${position.longitude}',
      );

      return position;
    } catch (e) {
      LoggerService.error('Location error: $e');
      rethrow;
    }
  }

  // Calculate prayer times with smart caching
  Future<PrayerTimesModel> calculatePrayerTimes({
    required double latitude,
    required double longitude,
    required String locationName,
    required String country,
    DateTime? date,
    String? calculationMethodName,
  }) async {
    try {
      final targetDate = date ?? DateTime.now();

      // Create cache key for today's prayer times
      final cacheKey =
          'prayer_times_${latitude.toStringAsFixed(3)}_${longitude.toStringAsFixed(3)}_${targetDate.day}_${targetDate.month}_${targetDate.year}';

      // Try to get cached prayer times first (for faster loading)
      final cachedData = _prefs.getString(cacheKey);
      if (cachedData != null) {
        try {
          final cachedJson = jsonDecode(cachedData) as Map<String, dynamic>;
          final cachedTimes = PrayerTimesModel.fromJson(cachedJson);

          // Check if cached data is still valid (same day)
          if (cachedTimes.date.day == targetDate.day &&
              cachedTimes.date.month == targetDate.month &&
              cachedTimes.date.year == targetDate.year) {
            LoggerService.cache('Using cached prayer times for faster loading');
            return cachedTimes;
          }
        } catch (e) {
          // Cache corrupted, continue with fresh calculation
          LoggerService.warning(
            'Cache corrupted, calculating fresh prayer times',
            e,
          );
        }
      }

      LoggerService.prayerTimes(
        'Calculating fresh prayer times with advanced SUNNI engine...',
      );

      // Clear old cache if location contains Mangaluru (force fresh calculation)
      if (locationName.toLowerCase().contains('mangaluru') ||
          locationName.toLowerCase().contains('mangalore')) {
        await _prefs.remove(cacheKey);
        LoggerService.prayerTimes(
          'Cleared cache for Mangaluru - ensuring fresh SUNNI calculation',
        );
      }

      // Create coordinates
      final coordinates = Coordinates(latitude, longitude);

      // Get the most accurate calculation method for this location
      final methodInfo = _getBestCalculationMethod(
        latitude,
        longitude,
        country,
        locationName,
      );
      LoggerService.prayerTimes(
        'Using ${methodInfo['name']} method for maximum accuracy',
      );

      // Calculate prayer times with the best method
      final prayerTimes = PrayerTimes.today(coordinates, methodInfo['params']);

      final result = PrayerTimesModel(
        fajr: prayerTimes.fajr,
        sunrise: prayerTimes.sunrise,
        dhuhr: prayerTimes.dhuhr,
        asr: prayerTimes.asr,
        maghrib: prayerTimes.maghrib,
        isha: prayerTimes.isha,
        date: targetDate,
        locationName: locationName,
        latitude: latitude,
        longitude: longitude,
        calculationMethod: methodInfo['name'],
        nextPrayer: _getNextPrayerName(prayerTimes),
        timeUntilNextPrayer: _getTimeUntilNextPrayer(prayerTimes),
      );

      // Cache the result for faster future loading
      await _prefs.setString(cacheKey, jsonEncode(result.toJson()));
      LoggerService.cache('Prayer times cached for faster loading');

      return result;
    } catch (e) {
      throw Exception('Failed to calculate prayer times: $e');
    }
  }

  // Advanced location-aware calculation method selection
  Map<String, dynamic> _getBestCalculationMethod(
    double latitude,
    double longitude,
    String country,
    String locationName,
  ) {
    final location = locationName.toLowerCase();
    final countryLower = country.toLowerCase();

    LoggerService.prayerTimes(
      'Analyzing location: $locationName, $country (${latitude.toStringAsFixed(2)}, ${longitude.toStringAsFixed(2)})',
    );

    // India - Enhanced calculation with regional optimization
    if (countryLower.contains('india') ||
        location.contains('mangalore') ||
        location.contains('mangaluru') ||
        location.contains('bangalore') ||
        location.contains('mumbai') ||
        location.contains('delhi') ||
        location.contains('chennai') ||
        location.contains('hyderabad') ||
        location.contains('kolkata') ||
        (latitude >= 6.0 &&
            latitude <= 37.0 &&
            longitude >= 68.0 &&
            longitude <= 97.0)) {
      // Special optimization for Mangaluru/Karnataka coastal region
      if (location.contains('mangalore') ||
          location.contains('mangaluru') ||
          location.contains('karnataka') ||
          (latitude >= 12.5 &&
              latitude <= 13.5 &&
              longitude >= 74.5 &&
              longitude <= 75.5)) {
        final params = CalculationMethod.karachi.getParameters();

        // SUNNI TIMING - Optimized for Mangaluru Sunni community
        params.madhab = Madhab.shafi; // Shafi madhab for Sunni community
        params.fajrAngle = 18.5; // Optimized for Mangaluru dawn timing
        params.ishaAngle = 17.0; // Optimized for Mangaluru twilight timing

        // Fine-tune for Mangaluru's specific latitude/longitude
        if (latitude >= 12.8 &&
            latitude <= 12.9 &&
            longitude >= 74.8 &&
            longitude <= 74.9) {
          // Very specific Mangaluru city SUNNI adjustments
          params.fajrAngle = 18.7; // Higher for accurate Sunni dawn
          params.ishaAngle = 17.2; // Adjusted for local Sunni twilight
        }

        LoggerService.prayerTimes(
          'Applied Mangaluru-specific Sunni timing optimization (Shafi madhab)',
        );

        return {
          'name': 'SUNNI - Mangaluru (Shafi Madhab)',
          'params': params,
          'region': 'Mangaluru Sunni Community',
        };
      }

      // General India - Use Hanafi for most of India
      final params = CalculationMethod.karachi.getParameters();
      params.madhab =
          Madhab.hanafi; // Hanafi madhab for most of Indian subcontinent

      // Regional adjustments for different parts of India
      if (latitude >= 28.0) {
        // Northern India (Delhi, Punjab, etc.)
        params.fajrAngle = 18.0;
        params.ishaAngle = 18.0;
      } else if (latitude <= 15.0) {
        // Southern India
        params.fajrAngle = 18.0;
        params.ishaAngle = 17.5; // Slightly adjusted for southern latitudes
      }

      return {
        'name': 'Karachi University (Hanafi) - India',
        'params': params,
        'region': 'Indian Subcontinent',
      };
    }

    // Pakistan, Bangladesh - Karachi method
    if (countryLower.contains('pakistan') ||
        countryLower.contains('bangladesh') ||
        (latitude >= 20.0 &&
            latitude <= 40.0 &&
            longitude >= 60.0 &&
            longitude <= 80.0)) {
      final params = CalculationMethod.karachi.getParameters();
      params.madhab = Madhab.hanafi;
      return {
        'name': 'Karachi University (Hanafi)',
        'params': params,
        'region': 'Pakistan/Bangladesh',
      };
    }

    // Saudi Arabia, UAE, Gulf - Use custom Umm al-Qura-like method
    if (countryLower.contains('saudi') ||
        countryLower.contains('uae') ||
        countryLower.contains('qatar') ||
        countryLower.contains('kuwait') ||
        countryLower.contains('bahrain') ||
        countryLower.contains('oman') ||
        (latitude >= 12.0 &&
            latitude <= 32.0 &&
            longitude >= 34.0 &&
            longitude <= 60.0)) {
      final params = CalculationMethod.karachi.getParameters();
      // Customize for Gulf region (similar to Umm al-Qura)
      params.fajrAngle = 18.5;
      params.ishaAngle = 17.0;
      params.madhab = Madhab.shafi; // Shafi madhab common in Gulf
      return {
        'name': 'Gulf Region (Umm al-Qura style)',
        'params': params,
        'region': 'Saudi Arabia/Gulf',
      };
    }

    // North America - Use custom ISNA-like method
    if (countryLower.contains('united states') ||
        countryLower.contains('canada') ||
        countryLower.contains('usa') ||
        countryLower.contains('america') ||
        (latitude >= 25.0 &&
            latitude <= 70.0 &&
            longitude >= -170.0 &&
            longitude <= -50.0)) {
      final params = CalculationMethod.karachi.getParameters();
      // Customize for North America (ISNA style)
      params.fajrAngle = 15.0;
      params.ishaAngle = 15.0;
      params.madhab = Madhab.hanafi;
      return {
        'name': 'North America (ISNA style)',
        'params': params,
        'region': 'North America',
      };
    }

    // Europe, Far East - Use custom Muslim World League-like method
    if (countryLower.contains('uk') ||
        countryLower.contains('germany') ||
        countryLower.contains('france') ||
        countryLower.contains('italy') ||
        countryLower.contains('spain') ||
        countryLower.contains('netherlands') ||
        countryLower.contains('japan') ||
        countryLower.contains('korea') ||
        countryLower.contains('china') ||
        countryLower.contains('malaysia') ||
        countryLower.contains('singapore') ||
        countryLower.contains('indonesia') ||
        (latitude >= 35.0 &&
            latitude <= 70.0 &&
            longitude >= -10.0 &&
            longitude <= 50.0) ||
        (latitude >= -10.0 &&
            latitude <= 50.0 &&
            longitude >= 95.0 &&
            longitude <= 180.0)) {
      final params = CalculationMethod.karachi.getParameters();
      // Customize for Europe/Far East (Muslim World League style)
      params.fajrAngle = 18.0;
      params.ishaAngle = 17.0;
      params.madhab = Madhab.hanafi;
      return {
        'name': 'Europe/Far East (Muslim World League style)',
        'params': params,
        'region': 'Europe/Far East',
      };
    }

    // Egypt, Middle East - Egyptian method
    if (countryLower.contains('egypt') ||
        countryLower.contains('syria') ||
        countryLower.contains('lebanon') ||
        countryLower.contains('jordan') ||
        countryLower.contains('iraq') ||
        countryLower.contains('palestine') ||
        (latitude >= 24.0 &&
            latitude <= 37.0 &&
            longitude >= 25.0 &&
            longitude <= 48.0)) {
      final params = CalculationMethod.egyptian.getParameters();
      return {
        'name': 'Egyptian General Authority of Survey',
        'params': params,
        'region': 'Egypt/Middle East',
      };
    }

    // Default fallback - Karachi method (widely accepted)
    LoggerService.prayerTimes(
      'Using default Karachi method for unrecognized location',
    );
    final params = CalculationMethod.karachi.getParameters();
    params.madhab = Madhab.hanafi;
    return {
      'name': 'Karachi University (Default)',
      'params': params,
      'region': 'Global Default',
    };
  }

  // Get next prayer name
  String _getNextPrayerName(PrayerTimes prayerTimes) {
    try {
      final next = prayerTimes.nextPrayer();
      return next.name;
    } catch (e) {
      return 'Fajr';
    }
  }

  // Get time until next prayer
  String _getTimeUntilNextPrayer(PrayerTimes prayerTimes) {
    try {
      final now = DateTime.now();
      final next = prayerTimes.nextPrayer();
      final nextPrayerTime = prayerTimes.timeForPrayer(next);

      if (nextPrayerTime != null) {
        final timeUntil = nextPrayerTime.difference(now);
        return '${timeUntil.inHours}h ${timeUntil.inMinutes % 60}m';
      }
      return '0h 0m';
    } catch (e) {
      return '0h 0m';
    }
  }

  // Helper method to process location position
  Future<LocationModel> _processLocationPosition(
    Position position,
    String fallbackAddress,
  ) async {
    try {
      // Get address from coordinates
      final placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        final city =
            placemark.locality ?? placemark.subAdministrativeArea ?? 'Unknown';
        final state = placemark.administrativeArea ?? 'Unknown';
        final country = placemark.country ?? 'Unknown';
        final fullAddress = '$city, $state, $country';

        final location = LocationModel(
          latitude: position.latitude,
          longitude: position.longitude,
          city: city,
          state: state,
          country: country,
          fullAddress: fullAddress,
        );

        // Save location for future use
        await _saveLocation(location);
        return location;
      } else {
        // Use fallback address
        final parts = fallbackAddress.split(', ');
        final location = LocationModel(
          latitude: position.latitude,
          longitude: position.longitude,
          city: parts.isNotEmpty ? parts[0] : 'Unknown',
          state: parts.length > 1 ? parts[1] : 'Unknown',
          country: parts.length > 2 ? parts[2] : 'Unknown',
          fullAddress: fallbackAddress,
        );

        await _saveLocation(location);
        return location;
      }
    } catch (e) {
      // Use fallback address if geocoding fails
      final parts = fallbackAddress.split(', ');
      final location = LocationModel(
        latitude: position.latitude,
        longitude: position.longitude,
        city: parts.isNotEmpty ? parts[0] : 'Unknown',
        state: parts.length > 1 ? parts[1] : 'Unknown',
        country: parts.length > 2 ? parts[2] : 'Unknown',
        fullAddress: fallbackAddress,
      );

      await _saveLocation(location);
      return location;
    }
  }

  // Save location
  Future<void> _saveLocation(LocationModel location) async {
    await _prefs.setString('saved_location', jsonEncode(location.toJson()));
  }

  // Get saved location
  Future<LocationModel?> getSavedLocation() async {
    try {
      final locationString = _prefs.getString('saved_location');
      if (locationString != null) {
        final locationJson = jsonDecode(locationString) as Map<String, dynamic>;
        return LocationModel.fromJson(locationJson);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Clear saved location (force fresh location detection)
  Future<void> clearSavedLocation() async {
    await _prefs.remove('saved_location');
  }

  // Get current location (fallback method without context)
  Future<LocationModel> getCurrentLocation() async {
    // Force user to use the context-aware method
    throw Exception(
      'Location permission required. Please allow location access and ensure GPS is enabled.',
    );
  }

  // Get prayer times for current location
  Future<PrayerTimesModel> getPrayerTimesForCurrentLocation({
    DateTime? date,
  }) async {
    final location = await getCurrentLocation();

    return calculatePrayerTimes(
      latitude: location.latitude,
      longitude: location.longitude,
      locationName: location.fullAddress,
      country: location.country,
      date: date,
    );
  }

  // Load prayer times for specific location
  Future<PrayerTimesModel> loadPrayerTimesForLocation({
    required double latitude,
    required double longitude,
    required String locationName,
    required String country,
    DateTime? date,
  }) async {
    return calculatePrayerTimes(
      latitude: latitude,
      longitude: longitude,
      locationName: locationName,
      country: country,
      date: date,
    );
  }

  // Get weekly prayer times
  Future<List<PrayerTimesModel>> getWeeklyPrayerTimes({
    required double latitude,
    required double longitude,
    required String locationName,
    required String country,
  }) async {
    final weeklyTimes = <PrayerTimesModel>[];
    final today = DateTime.now();

    for (int i = 0; i < 7; i++) {
      final date = today.add(Duration(days: i));
      final prayerTimes = await calculatePrayerTimes(
        latitude: latitude,
        longitude: longitude,
        locationName: locationName,
        country: country,
        date: date,
      );
      weeklyTimes.add(prayerTimes);
    }

    return weeklyTimes;
  }
}
