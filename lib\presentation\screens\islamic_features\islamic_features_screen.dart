import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/router/app_router.dart';
import '../../cubit/theme/theme_cubit.dart';
import '../../widgets/gradient_background.dart';
import '../../widgets/feature_card.dart';

class IslamicFeaturesScreen extends StatelessWidget {
  const IslamicFeaturesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: SafeArea(
          child: Column(
            children: [
              // Enhanced Header
              _buildEnhancedHeader(),

              // Features Section
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Daily Essentials Section
                      _buildSectionTitle('Daily Essentials'),
                      SizedBox(height: 16.h),
                      _buildFeaturesGrid([
                        0,
                        1,
                        2,
                        3,
                      ]), // Prayer, Qibla, Mosque, Quran

                      SizedBox(height: 32.h),

                      // Learning & Spirituality Section
                      _buildSectionTitle('Learning & Spirituality'),
                      SizedBox(height: 16.h),
                      _buildFeaturesGrid([
                        4,
                        5,
                        6,
                        7,
                      ]), // Tafsir, Hadith, Duas, 99 Names

                      SizedBox(height: 20.h),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getFeatureIcon(int index) {
    switch (index) {
      case 0:
        return Icons.access_time;
      case 1:
        return Icons.explore;
      case 2:
        return Icons.location_on;
      case 3:
        return Icons.menu_book;
      case 4:
        return Icons.book;
      case 5:
        return Icons.library_books;
      case 6:
        return Icons.favorite;
      case 7:
        return Icons.star;
      default:
        return Icons.star;
    }
  }

  List<Color> _getFeatureGradient(int index) {
    switch (index) {
      case 0:
        return [
          AppColors.fajrColor,
          AppColors.fajrColor.withValues(alpha: 0.7),
        ];
      case 1:
        return [AppColors.primaryGreen, AppColors.primaryGreenLight];
      case 2:
        return [
          AppColors.dhuhrColor,
          AppColors.dhuhrColor.withValues(alpha: 0.7),
        ];
      case 3:
        return [AppColors.asrColor, AppColors.asrColor.withValues(alpha: 0.7)];
      case 4:
        return [
          AppColors.maghribColor,
          AppColors.maghribColor.withValues(alpha: 0.7),
        ];
      case 5:
        return [
          AppColors.ishaColor,
          AppColors.ishaColor.withValues(alpha: 0.7),
        ];
      case 6:
        return [AppColors.secondaryGold, AppColors.secondaryGoldLight];
      case 7:
        return AppColors.primaryGradient;
      default:
        return AppColors.primaryGradient;
    }
  }

  Widget _buildEnhancedHeader() {
    return Builder(
      builder:
          (context) => Container(
            margin: EdgeInsets.fromLTRB(20.w, 20.w, 20.w, 10.w),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  Theme.of(
                    context,
                  ).colorScheme.secondary.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(24.r),
              border: Border.all(
                color: Theme.of(
                  context,
                ).colorScheme.primary.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                // Top Row with greeting and controls
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Islamic Greeting with Icon
                          Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(8.w),
                                decoration: BoxDecoration(
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.primary.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                child: Icon(
                                  Icons.mosque,
                                  color: Theme.of(context).colorScheme.primary,
                                  size: 20.sp,
                                ),
                              ),
                              SizedBox(width: 12.w),
                              Flexible(
                                child: Text(
                                  'Assalamu Alaikum',
                                  style: AppTextStyles.headlineSmall.copyWith(
                                    color:
                                        Theme.of(context).colorScheme.onSurface,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 8.h),
                          // Welcome message
                          Text(
                            'Welcome to your Islamic companion',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Theme.of(
                                context,
                              ).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 16.w),
                    // Control buttons
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        BlocBuilder<ThemeCubit, ThemeState>(
                          builder: (context, state) {
                            return Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surface,
                                borderRadius: BorderRadius.circular(12.r),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.1),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: IconButton(
                                onPressed: () {
                                  context.read<ThemeCubit>().toggleTheme();
                                },
                                icon: Icon(
                                  state.isDarkMode
                                      ? Icons.light_mode
                                      : Icons.dark_mode,
                                  color: Theme.of(context).colorScheme.primary,
                                  size: 22.sp,
                                ),
                              ),
                            );
                          },
                        ),
                        SizedBox(width: 8.w),
                        Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: BorderRadius.circular(12.r),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: IconButton(
                            onPressed: () {
                              context.push(AppRouter.settings);
                            },
                            icon: Icon(
                              Icons.settings,
                              color: Theme.of(context).colorScheme.primary,
                              size: 22.sp,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Builder(
      builder:
          (context) => Container(
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 8.h),
            child: Text(
              title,
              style: AppTextStyles.titleLarge.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
    );
  }

  Widget _buildFeaturesGrid(List<int> indices) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16.w,
        mainAxisSpacing: 16.h,
        childAspectRatio: 1.2, // Increased for better text space
      ),
      itemCount: indices.length,
      itemBuilder: (context, gridIndex) {
        final index = indices[gridIndex];
        return FeatureCard(
          title: AppConstants.islamicFeatures[index],
          icon: _getFeatureIcon(index),
          gradient: _getFeatureGradient(index),
          onTap: () => _handleFeatureTap(context, index),
          index: gridIndex,
        );
      },
    );
  }

  void _handleFeatureTap(BuildContext context, int index) {
    // Navigate to specific feature screens
    switch (index) {
      case 0:
        // Navigate to Prayer Times
        context.push(AppRouter.prayerTimes);
        break;
      case 1:
        // Navigate to Qibla Direction
        context.push('/qibla');
        break;
      case 2:
        // Navigate to Mosque Finder
        context.push('/mosque-finder');
        break;
      case 3:
        // Navigate to Quran with quick initialization
        context.push('/quran?quickInit=true');
        break;
      case 4:
        // Navigate to Tafsir
        context.push('/tafsir');
        break;
      case 5:
        // Navigate to Hadith
        context.push('/hadith');
        break;
      case 6:
        // Navigate to Duas
        context.push('/duas');
        break;
      case 7:
        // Navigate to 99 Names
        context.push('/names-of-allah');
        break;
      default:
        // Show coming soon message for unimplemented features
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${AppConstants.islamicFeatures[index]} feature coming soon!',
            ),
            backgroundColor: AppColors.primaryGreen,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
        );
    }
  }
}
