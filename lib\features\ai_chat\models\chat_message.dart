import 'package:equatable/equatable.dart';

/// Chat message model for AI conversations
class ChatMessage extends Equatable {
  final String id;
  final String content;
  final bool isUser;
  final DateTime timestamp;
  final MessageStatus status;
  final MessageType type;

  const ChatMessage({
    required this.id,
    required this.content,
    required this.isUser,
    required this.timestamp,
    this.status = MessageStatus.sent,
    this.type = MessageType.text,
  });

  @override
  List<Object?> get props => [id, content, isUser, timestamp, status, type];

  /// Create a user message
  factory ChatMessage.user({
    required String content,
    String? id,
  }) {
    return ChatMessage(
      id: id ?? 'user_${DateTime.now().millisecondsSinceEpoch}',
      content: content,
      isUser: true,
      timestamp: DateTime.now(),
      status: MessageStatus.sent,
      type: MessageType.text,
    );
  }

  /// Create an AI message
  factory ChatMessage.ai({
    required String content,
    String? id,
    MessageStatus status = MessageStatus.sent,
  }) {
    return ChatMessage(
      id: id ?? 'ai_${DateTime.now().millisecondsSinceEpoch}',
      content: content,
      isUser: false,
      timestamp: DateTime.now(),
      status: status,
      type: MessageType.text,
    );
  }

  /// Create a system message (like welcome message)
  factory ChatMessage.system({
    required String content,
    String? id,
  }) {
    return ChatMessage(
      id: id ?? 'system_${DateTime.now().millisecondsSinceEpoch}',
      content: content,
      isUser: false,
      timestamp: DateTime.now(),
      status: MessageStatus.sent,
      type: MessageType.system,
    );
  }

  /// Copy with new properties
  ChatMessage copyWith({
    String? id,
    String? content,
    bool? isUser,
    DateTime? timestamp,
    MessageStatus? status,
    MessageType? type,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      isUser: isUser ?? this.isUser,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      type: type ?? this.type,
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'isUser': isUser,
      'timestamp': timestamp.toIso8601String(),
      'status': status.name,
      'type': type.name,
    };
  }

  /// Create from JSON
  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'] as String,
      content: json['content'] as String,
      isUser: json['isUser'] as bool,
      timestamp: DateTime.parse(json['timestamp'] as String),
      status: MessageStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => MessageStatus.sent,
      ),
      type: MessageType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => MessageType.text,
      ),
    );
  }

  /// Check if message is from AI
  bool get isAi => !isUser && type != MessageType.system;

  /// Check if message is system message
  bool get isSystem => type == MessageType.system;

  /// Get formatted timestamp
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

/// Message status enumeration
enum MessageStatus {
  sending,
  sent,
  delivered,
  failed,
  streaming,
}

/// Message type enumeration
enum MessageType {
  text,
  system,
  error,
  thinking,
}

/// Extension for message status
extension MessageStatusExtension on MessageStatus {
  bool get isSending => this == MessageStatus.sending;
  bool get isSent => this == MessageStatus.sent;
  bool get isDelivered => this == MessageStatus.delivered;
  bool get isFailed => this == MessageStatus.failed;
  bool get isStreaming => this == MessageStatus.streaming;
}

/// Extension for message type
extension MessageTypeExtension on MessageType {
  bool get isText => this == MessageType.text;
  bool get isSystem => this == MessageType.system;
  bool get isError => this == MessageType.error;
  bool get isThinking => this == MessageType.thinking;
}
