class DuaService {
  // Authentic duas for each prayer time - verified for accuracy
  static const Map<String, Map<String, String>> prayerDuas = {
    'Fajr': {
      'arabic':
          'اللَّهُمَّ بَارِكْ لَنَا فِيمَا رَزَقْتَنَا وَقِنَا عَذَابَ النَّارِ',
      'transliteration':
          '<PERSON><PERSON><PERSON> baarik lanaa feemaa razaqtanaa wa qinaa \'adhaab an-naar',
      'english':
          'O <PERSON>, bless us in what You have provided us and protect us from the punishment of the Fire',
    },
    'Sunrise': {
      'arabic': 'أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ رَبِّ الْعَالَمِينَ',
      'transliteration': 'Asbahna wa asbahal-mulku lillaahi rabbil-\'aalameen',
      'english':
          'We have entered morning and the kingdom belongs to <PERSON>, Lord of the worlds',
    },
    'Dhuhr': {
      'arabic':
          'اللَّهُمَّ أَعِنِّي عَلَى ذِكْرِكَ وَشُكْرِكَ وَحُسْنِ عِبَادَتِكَ',
      'transliteration':
          '<PERSON><PERSON><PERSON> a\'innee \'alaa dhikrika wa shukrika wa husni \'ibaadatik',
      'english':
          'O <PERSON>, help me to remember You, thank You, and worship You in the best manner',
    },
    'Asr': {
      'arabic':
          'اللَّهُمَّ إِنِّي أَسْأَلُكَ مِنْ فَضْلِكَ وَرَحْمَتِكَ فَإِنَّهُ لَا يَمْلِكُهَا إِلَّا أَنْتَ',
      'transliteration':
          'Allahumma innee as\'aluka min fadlika wa rahmatika fa innahu laa yamlikuhaa illaa ant',
      'english':
          'O Allah, I ask You for Your grace and mercy, for none possesses them except You',
    },
    'Maghrib': {
      'arabic': 'أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ رَبِّ الْعَالَمِينَ',
      'transliteration': 'Amsaynaa wa amsal-mulku lillaahi rabbil-\'aalameen',
      'english':
          'We have entered evening and the kingdom belongs to Allah, Lord of the worlds',
    },
    'Isha': {
      'arabic': 'اللَّهُمَّ أَجِرْنِي مِنَ النَّارِ وَأَدْخِلْنِي الْجَنَّةَ',
      'transliteration': 'Allahumma ajirnee minan-naari wa adkhilneel-jannah',
      'english': 'O Allah, protect me from the Fire and admit me into Paradise',
    },
  };

  // Get dua for specific prayer
  static Map<String, String>? getDuaForPrayer(String prayerName) {
    return prayerDuas[prayerName];
  }

  // Get all duas
  static Map<String, Map<String, String>> getAllDuas() {
    return prayerDuas;
  }

  // Get random dua
  static Map<String, String> getRandomDua() {
    final prayers = prayerDuas.keys.toList();
    prayers.shuffle();
    return prayerDuas[prayers.first]!;
  }

  // Check if prayer has dua
  static bool hasDua(String prayerName) {
    return prayerDuas.containsKey(prayerName);
  }
}
