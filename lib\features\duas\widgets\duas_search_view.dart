import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/dua_models.dart';
import '../cubit/duas_cubit.dart';

class DuasSearchView extends StatelessWidget {
  final String query;
  final List<DuaModel> results;

  const DuasSearchView({
    super.key,
    required this.query,
    required this.results,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Search Results Header
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
          child: Text(
            'Search Results for "$query"',
            style: AppTextStyles.headlineSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Text(
            '${results.length} duas found',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.grey,
            ),
          ),
        ),
        
        SizedBox(height: 16.h),

        // Results List
        if (results.isEmpty)
          _buildEmptyState(context)
        else
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              itemCount: results.length,
              itemBuilder: (context, index) {
                final dua = results[index];
                return _buildSearchResultCard(context, dua);
              },
            ),
          ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64.sp,
              color: Colors.grey,
            ),
            SizedBox(height: 16.h),
            Text(
              'No duas found',
              style: AppTextStyles.headlineSmall.copyWith(
                color: Colors.grey,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Try searching with different keywords',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResultCard(BuildContext context, DuaModel dua) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => context.read<DuasCubit>().viewDua(dua),
        borderRadius: BorderRadius.circular(16.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and favorite
              Row(
                children: [
                  Expanded(
                    child: Text(
                      dua.title,
                      style: AppTextStyles.labelLarge.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  IconButton(
                    onPressed: () => context.read<DuasCubit>().toggleFavorite(dua),
                    icon: Icon(
                      dua.isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: dua.isFavorite ? Colors.red : Colors.grey,
                      size: 24.sp,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 8.h),

              // Arabic text preview
              Text(
                dua.arabicText.length > 80 
                    ? '${dua.arabicText.substring(0, 80)}...'
                    : dua.arabicText,
                style: AppTextStyles.bodyLarge.copyWith(
                  fontFamily: 'Arabic',
                  height: 1.8,
                  color: AppColors.primaryGreen,
                ),
                textAlign: TextAlign.right,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              SizedBox(height: 8.h),

              // English translation preview
              Text(
                dua.englishTranslation.length > 100
                    ? '${dua.englishTranslation.substring(0, 100)}...'
                    : dua.englishTranslation,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[600],
                  height: 1.4,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              SizedBox(height: 8.h),

              // Simple explanation preview
              Text(
                dua.simpleExplanation.length > 80
                    ? '${dua.simpleExplanation.substring(0, 80)}...'
                    : dua.simpleExplanation,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[500],
                  fontStyle: FontStyle.italic,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              SizedBox(height: 12.h),

              // Benefits preview
              if (dua.benefits.isNotEmpty) ...[
                Row(
                  children: [
                    Icon(
                      Icons.star,
                      color: Colors.amber,
                      size: 16.sp,
                    ),
                    SizedBox(width: 4.w),
                    Expanded(
                      child: Text(
                        dua.benefits.first,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.amber[700],
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
