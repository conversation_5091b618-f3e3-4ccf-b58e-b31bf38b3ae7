import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/quran_models.dart';
import '../cubit/quran_cubit.dart';

class SurahsListView extends StatefulWidget {
  final List<SurahModel> surahs;
  final QuranReadingPreferences preferences;
  final Function(SurahModel) onSurahTap;
  final Function(String) onSearch;

  const SurahsListView({
    super.key,
    required this.surahs,
    required this.preferences,
    required this.onSurahTap,
    required this.onSearch,
  });

  @override
  State<SurahsListView> createState() => _SurahsListViewState();
}

class _SurahsListViewState extends State<SurahsListView> {
  late TextEditingController _searchController;
  List<SurahModel> _filteredSurahs = [];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _filteredSurahs = widget.surahs;
  }

  @override
  void didUpdateWidget(SurahsListView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.surahs != widget.surahs) {
      _filteredSurahs = widget.surahs;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterSurahs(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredSurahs = widget.surahs;
      } else {
        _filteredSurahs =
            widget.surahs.where((surah) {
              return surah.englishName.toLowerCase().contains(
                    query.toLowerCase(),
                  ) ||
                  surah.name.contains(query) ||
                  surah.number.toString().contains(query);
            }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search bar
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
          child: _buildSearchBar(),
        ),

        // Surahs count
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Row(
            children: [
              Text(
                '${_filteredSurahs.length} Surahs',
                style: AppTextStyles.bodyMedium.copyWith(
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.8)
                          : Colors.black.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: 16.h),

        // Surahs list
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            itemCount: _filteredSurahs.length,
            itemBuilder: (context, index) {
              final surah = _filteredSurahs[index];
              return _buildSurahCard(surah, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? Colors.white.withValues(alpha: 0.1)
                : Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withValues(alpha: 0.2)
                  : Colors.black.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        onChanged: _filterSurahs,
        style: AppTextStyles.bodyMedium.copyWith(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black87,
        ),
        decoration: InputDecoration(
          hintText: 'Search Surahs...',
          hintStyle: AppTextStyles.bodyMedium.copyWith(
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? Colors.white.withValues(alpha: 0.6)
                    : Colors.black.withValues(alpha: 0.5),
          ),
          prefixIcon: Icon(
            Icons.search,
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? Colors.white.withValues(alpha: 0.6)
                    : Colors.black.withValues(alpha: 0.5),
            size: 20.sp,
          ),
          suffixIcon:
              _searchController.text.isNotEmpty
                  ? IconButton(
                    onPressed: () {
                      _searchController.clear();
                      _filterSurahs('');
                    },
                    icon: Icon(
                      Icons.clear,
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.6)
                              : Colors.black.withValues(alpha: 0.5),
                      size: 20.sp,
                    ),
                  )
                  : null,
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 12.h,
          ),
        ),
      ),
    );
  }

  Widget _buildSurahCard(SurahModel surah, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? Colors.white.withValues(alpha: 0.1)
                : Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withValues(alpha: 0.2)
                  : Colors.black.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => widget.onSurahTap(surah),
          borderRadius: BorderRadius.circular(16.r),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                // Surah number
                Container(
                  width: 48.w,
                  height: 48.w,
                  decoration: BoxDecoration(
                    color: AppColors.primaryGreen.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Center(
                    child: Text(
                      surah.number.toString(),
                      style: AppTextStyles.titleMedium.copyWith(
                        color: AppColors.primaryGreen,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                ),

                SizedBox(width: 16.w),

                // Surah info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // English name
                      Text(
                        surah.englishName,
                        style: AppTextStyles.titleMedium.copyWith(
                          color:
                              Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white
                                  : Colors.black87,
                          fontWeight: FontWeight.w600,
                        ),
                      ),

                      SizedBox(height: 2.h),

                      // Translation and type
                      Text(
                        surah.englishNameTranslation,
                        style: AppTextStyles.bodySmall.copyWith(
                          color:
                              Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white.withValues(alpha: 0.7)
                                  : Colors.black.withValues(alpha: 0.6),
                        ),
                      ),

                      SizedBox(height: 4.h),

                      // Revelation type and ayahs count
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 6.w,
                              vertical: 2.h,
                            ),
                            decoration: BoxDecoration(
                              color:
                                  surah.revelationType == 'Meccan'
                                      ? AppColors.secondaryGold.withValues(
                                        alpha: 0.2,
                                      )
                                      : AppColors.primaryGreen.withValues(
                                        alpha: 0.2,
                                      ),
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                            child: Text(
                              surah.revelationType,
                              style: AppTextStyles.bodySmall.copyWith(
                                color:
                                    surah.revelationType == 'Meccan'
                                        ? AppColors.secondaryGold
                                        : AppColors.primaryGreen,
                                fontSize: 10.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),

                          SizedBox(width: 8.w),

                          Text(
                            '${surah.numberOfAyahs} Ayahs',
                            style: AppTextStyles.bodySmall.copyWith(
                              color:
                                  Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? Colors.white.withValues(alpha: 0.6)
                                      : Colors.black.withValues(alpha: 0.5),
                              fontSize: 10.sp,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Arabic name and bookmark
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      surah.name,
                      style: AppTextStyles.titleLarge.copyWith(
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.white
                                : Colors.black87,
                        fontWeight: FontWeight.w600,
                        fontSize: 20.sp,
                      ),
                      textDirection: TextDirection.rtl,
                    ),

                    SizedBox(height: 8.h),

                    // Bookmark button
                    _BookmarkButton(surah: surah),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Optimized bookmark button widget
class _BookmarkButton extends StatefulWidget {
  final SurahModel surah;

  const _BookmarkButton({required this.surah});

  @override
  State<_BookmarkButton> createState() => _BookmarkButtonState();
}

class _BookmarkButtonState extends State<_BookmarkButton> {
  bool _isBookmarked = false;

  @override
  void initState() {
    super.initState();
    _updateBookmarkStatus();
  }

  void _updateBookmarkStatus() {
    final bookmarks = context.read<QuranCubit>().bookmarks;
    setState(() {
      _isBookmarked = bookmarks.any(
        (bookmark) => bookmark.surahNumber == widget.surah.number,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        // Optimistic update for smooth UI
        setState(() {
          _isBookmarked = !_isBookmarked;
        });

        // Perform actual bookmark toggle
        await context.read<QuranCubit>().toggleSurahBookmark(widget.surah);

        // Update status from actual data
        _updateBookmarkStatus();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.all(6.w),
        decoration: BoxDecoration(
          color:
              _isBookmarked
                  ? AppColors.primaryGreen.withValues(alpha: 0.2)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color:
                _isBookmarked
                    ? AppColors.primaryGreen
                    : (Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.3)
                        : Colors.black.withValues(alpha: 0.3)),
            width: 1,
          ),
        ),
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 200),
          child: Icon(
            _isBookmarked ? Icons.bookmark : Icons.bookmark_border,
            key: ValueKey(_isBookmarked),
            size: 18.sp,
            color:
                _isBookmarked
                    ? AppColors.primaryGreen
                    : (Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.7)
                        : Colors.black.withValues(alpha: 0.7)),
          ),
        ),
      ),
    );
  }
}
