import 'package:equatable/equatable.dart';

/// Comprehensive mosque model with all necessary data
class MosqueModel extends Equatable {
  final String id;
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  final double? distanceFromUser; // in kilometers
  final String? phone;
  final String? website;
  final String? description;
  final List<String> facilities;
  final MosqueTimings? timings;
  final List<String> photos;
  final double? rating;
  final int? reviewCount;
  final bool isOpen;
  final String? denomination; // Sunni, Shia, etc.

  const MosqueModel({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    this.distanceFromUser,
    this.phone,
    this.website,
    this.description,
    this.facilities = const [],
    this.timings,
    this.photos = const [],
    this.rating,
    this.reviewCount,
    this.isOpen = true,
    this.denomination,
  });

  /// Create mosque from OpenStreetMap data
  factory MosqueModel.fromOSM(Map<String, dynamic> osmData) {
    final tags = osmData['tags'] as Map<String, dynamic>? ?? {};
    final lat = osmData['lat'] as double? ?? 0.0;
    final lon = osmData['lon'] as double? ?? 0.0;
    
    return MosqueModel(
      id: osmData['id']?.toString() ?? '',
      name: tags['name'] ?? tags['name:en'] ?? 'Mosque',
      address: _buildAddress(tags),
      latitude: lat,
      longitude: lon,
      phone: tags['phone'] ?? tags['contact:phone'],
      website: tags['website'] ?? tags['contact:website'],
      description: tags['description'],
      facilities: _extractFacilities(tags),
      denomination: tags['denomination'],
    );
  }

  /// Build address from OSM tags
  static String _buildAddress(Map<String, dynamic> tags) {
    final parts = <String>[];
    
    if (tags['addr:housenumber'] != null) parts.add(tags['addr:housenumber']);
    if (tags['addr:street'] != null) parts.add(tags['addr:street']);
    if (tags['addr:city'] != null) parts.add(tags['addr:city']);
    if (tags['addr:state'] != null) parts.add(tags['addr:state']);
    if (tags['addr:postcode'] != null) parts.add(tags['addr:postcode']);
    
    return parts.isNotEmpty ? parts.join(', ') : 'Address not available';
  }

  /// Extract facilities from OSM tags
  static List<String> _extractFacilities(Map<String, dynamic> tags) {
    final facilities = <String>[];
    
    if (tags['wheelchair'] == 'yes') facilities.add('Wheelchair Accessible');
    if (tags['parking'] == 'yes') facilities.add('Parking Available');
    if (tags['female'] == 'yes') facilities.add('Women\'s Section');
    if (tags['wudu'] == 'yes') facilities.add('Wudu Facilities');
    if (tags['air_conditioning'] == 'yes') facilities.add('Air Conditioning');
    if (tags['heating'] == 'yes') facilities.add('Heating');
    
    return facilities;
  }

  /// Copy with method for updating distance
  MosqueModel copyWith({
    String? id,
    String? name,
    String? address,
    double? latitude,
    double? longitude,
    double? distanceFromUser,
    String? phone,
    String? website,
    String? description,
    List<String>? facilities,
    MosqueTimings? timings,
    List<String>? photos,
    double? rating,
    int? reviewCount,
    bool? isOpen,
    String? denomination,
  }) {
    return MosqueModel(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      distanceFromUser: distanceFromUser ?? this.distanceFromUser,
      phone: phone ?? this.phone,
      website: website ?? this.website,
      description: description ?? this.description,
      facilities: facilities ?? this.facilities,
      timings: timings ?? this.timings,
      photos: photos ?? this.photos,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isOpen: isOpen ?? this.isOpen,
      denomination: denomination ?? this.denomination,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        address,
        latitude,
        longitude,
        distanceFromUser,
        phone,
        website,
        description,
        facilities,
        timings,
        photos,
        rating,
        reviewCount,
        isOpen,
        denomination,
      ];
}

/// Mosque prayer timings
class MosqueTimings extends Equatable {
  final String? fajr;
  final String? dhuhr;
  final String? asr;
  final String? maghrib;
  final String? isha;
  final String? jumma; // Friday prayer time

  const MosqueTimings({
    this.fajr,
    this.dhuhr,
    this.asr,
    this.maghrib,
    this.isha,
    this.jumma,
  });

  factory MosqueTimings.fromMap(Map<String, dynamic> map) {
    return MosqueTimings(
      fajr: map['fajr'],
      dhuhr: map['dhuhr'],
      asr: map['asr'],
      maghrib: map['maghrib'],
      isha: map['isha'],
      jumma: map['jumma'],
    );
  }

  @override
  List<Object?> get props => [fajr, dhuhr, asr, maghrib, isha, jumma];
}

/// Search filters for mosque finder
class MosqueSearchFilters extends Equatable {
  final double maxDistance; // in kilometers
  final List<String> requiredFacilities;
  final String? denomination;
  final bool openNow;

  const MosqueSearchFilters({
    this.maxDistance = 10.0,
    this.requiredFacilities = const [],
    this.denomination,
    this.openNow = false,
  });

  MosqueSearchFilters copyWith({
    double? maxDistance,
    List<String>? requiredFacilities,
    String? denomination,
    bool? openNow,
  }) {
    return MosqueSearchFilters(
      maxDistance: maxDistance ?? this.maxDistance,
      requiredFacilities: requiredFacilities ?? this.requiredFacilities,
      denomination: denomination ?? this.denomination,
      openNow: openNow ?? this.openNow,
    );
  }

  @override
  List<Object?> get props => [maxDistance, requiredFacilities, denomination, openNow];
}
