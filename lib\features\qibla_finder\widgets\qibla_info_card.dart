import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/qibla_model.dart';

class QiblaInfoCard extends StatelessWidget {
  final QiblaModel qiblaData;

  const QiblaInfoCard({super.key, required this.qiblaData});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? Colors.white.withValues(alpha: 0.1)
                : Colors.black.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withValues(alpha: 0.2)
                  : Colors.black.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Location info
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: AppColors.secondaryGold,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  qiblaData.locationName,
                  style: AppTextStyles.titleMedium.copyWith(
                    color:
                        Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black87,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Info grid
          Row(
            children: [
              // Distance to Kaaba
              Expanded(
                child: _buildInfoItem(
                  context: context,
                  icon: Icons.straighten,
                  label: 'Distance',
                  value: '${qiblaData.distanceToKaaba.toStringAsFixed(0)} km',
                  color: AppColors.primaryGreen,
                ),
              ),

              SizedBox(width: 16.w),

              // Qibla direction
              Expanded(
                child: _buildInfoItem(
                  context: context,
                  icon: Icons.explore,
                  label: 'Direction',
                  value:
                      '${qiblaData.qiblaDirection.toStringAsFixed(0)}° ${qiblaData.directionDescription}',
                  color: AppColors.secondaryGold,
                ),
              ),
            ],
          ),

          SizedBox(height: 12.h),

          // Accuracy info
          Row(
            children: [
              Icon(
                Icons.gps_fixed,
                color: Colors.white.withValues(alpha: 0.7),
                size: 16.sp,
              ),
              SizedBox(width: 6.w),
              Text(
                'Accuracy: ±${qiblaData.magneticDeclination.abs().toStringAsFixed(1)}° (Magnetic declination adjusted)',
                style: AppTextStyles.bodySmall.copyWith(
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.7)
                          : Colors.black.withValues(alpha: 0.6),
                  fontSize: 11.sp,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required BuildContext context,
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16.sp),
              SizedBox(width: 6.w),
              Text(
                label,
                style: AppTextStyles.bodySmall.copyWith(
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.8)
                          : Colors.black.withValues(alpha: 0.6),
                  fontSize: 11.sp,
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: AppTextStyles.titleSmall.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
