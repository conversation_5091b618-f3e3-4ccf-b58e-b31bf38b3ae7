import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hijri/hijri_calendar.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../presentation/widgets/gradient_background.dart';
import '../cubit/prayer_times_cubit.dart';
import '../models/prayer_times_model.dart';
import '../widgets/prayer_times_loading.dart' show PrayerTimesLoadingWidget;
import '../services/dua_service.dart';

class PrayerTimesScreen extends StatefulWidget {
  const PrayerTimesScreen({super.key});

  @override
  State<PrayerTimesScreen> createState() => _PrayerTimesScreenState();
}

class _PrayerTimesScreenState extends State<PrayerTimesScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  bool _showArabic = true; // Default to Arabic as requested

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadPrayerTimes();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );
  }

  void _loadPrayerTimes() {
    // First try to load with current location (with permission handling)
    context.read<PrayerTimesCubit>().getCurrentLocationAndLoadPrayerTimes(
      context,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: SafeArea(
          child: Column(
            children: [
              _buildAppBar(),
              Expanded(
                child: BlocBuilder<PrayerTimesCubit, PrayerTimesState>(
                  builder: (context, state) {
                    if (state is PrayerTimesLoading) {
                      return const PrayerTimesLoadingWidget();
                    } else if (state is PrayerTimesLoaded) {
                      return _buildLoadedState(state);
                    } else if (state is PrayerTimesError) {
                      return _buildErrorState(state);
                    }
                    return _buildInitialState();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: EdgeInsets.all(20.w),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(
              Icons.arrow_back_ios,
              color: Theme.of(context).colorScheme.onSurface,
              size: 24.sp,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              'Prayer Times',
              style: AppTextStyles.headlineSmall.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: () {
                  // Try to get real location again
                  context
                      .read<PrayerTimesCubit>()
                      .getCurrentLocationAndLoadPrayerTimes(context);
                },
                icon: Icon(
                  Icons.my_location,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24.sp,
                ),
                tooltip: 'Get My Location',
              ),
              IconButton(
                onPressed: () {
                  // Refresh current prayer times
                  context.read<PrayerTimesCubit>().refreshPrayerTimes();
                },
                icon: Icon(
                  Icons.refresh,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24.sp,
                ),
                tooltip: 'Refresh',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(PrayerTimesError state) {
    // Detect if this is a location-related error
    final isLocationError =
        state.message.toLowerCase().contains('location') ||
        state.message.toLowerCase().contains('gps') ||
        state.message.toLowerCase().contains('permission');

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Different icon for location vs general errors
            Container(
              width: 80.w,
              height: 80.w,
              decoration: BoxDecoration(
                color: AppColors.error.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(40.r),
                border: Border.all(
                  color: AppColors.error.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
              child: Icon(
                isLocationError ? Icons.location_off : Icons.error_outline,
                size: 40.sp,
                color: AppColors.error,
              ),
            ),
            SizedBox(height: 24.h),
            Text(
              isLocationError
                  ? 'Location Required'
                  : 'Error Loading Prayer Times',
              style: AppTextStyles.titleLarge.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            Text(
              isLocationError
                  ? 'Please enable GPS/Location services in your device settings to get accurate prayer times for your location.'
                  : state.message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32.h),

            // Enhanced buttons for location errors
            if (isLocationError) ...[
              ElevatedButton.icon(
                onPressed: () {
                  // Force fresh location check with native permission dialog
                  context
                      .read<PrayerTimesCubit>()
                      .getCurrentLocationAndLoadPrayerTimes(context);
                },
                icon: Icon(Icons.refresh, size: 20.sp),
                label: Text('Check Again'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryGreen,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    horizontal: 24.w,
                    vertical: 12.h,
                  ),
                ),
              ),
              SizedBox(height: 12.h),
              OutlinedButton.icon(
                onPressed: () async {
                  // Open device location settings
                  await Geolocator.openLocationSettings();
                },
                icon: Icon(Icons.settings, size: 20.sp),
                label: Text('Open Settings'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.primaryGreen,
                  side: BorderSide(color: AppColors.primaryGreen),
                  padding: EdgeInsets.symmetric(
                    horizontal: 24.w,
                    vertical: 12.h,
                  ),
                ),
              ),
            ] else ...[
              ElevatedButton.icon(
                onPressed: () {
                  // Force fresh location check for any error
                  context
                      .read<PrayerTimesCubit>()
                      .getCurrentLocationAndLoadPrayerTimes(context);
                },
                icon: Icon(Icons.refresh, size: 20.sp),
                label: Text('Try Again'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryGreen,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    horizontal: 24.w,
                    vertical: 12.h,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInitialState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.mosque,
            size: 64.sp,
            color: Theme.of(context).colorScheme.primary,
          ),
          SizedBox(height: 20.h),
          Text(
            'Welcome to Prayer Times',
            style: AppTextStyles.titleLarge.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 12.h),
          Text(
            'Tap to load prayer times for your location',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 24.h),
          ElevatedButton(
            onPressed: _loadPrayerTimes,
            child: Text('Load Prayer Times'),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadedState(PrayerTimesLoaded state) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(20.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildLocationCard(state.prayerTimes),
              SizedBox(height: 20.h),
              _buildNextPrayerCard(state.nextPrayer),
              SizedBox(height: 20.h),
              _buildCurrentPrayerCard(state.currentPrayer),
              SizedBox(height: 20.h),
              _buildPrayerTimesList(state.prayerTimes),
              SizedBox(height: 20.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLocationCard(PrayerTimesModel prayerTimes) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: AppColors.primaryGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGreen.withValues(alpha: 0.25),
            blurRadius: 20,
            offset: const Offset(0, 10),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Icon(Icons.location_on, color: Colors.white, size: 24.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  prayerTimes.locationName,
                  style: AppTextStyles.titleLarge.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                    letterSpacing: 0.5,
                  ),
                ),
                SizedBox(height: 6.h),
                Text(
                  DateFormat('EEEE, MMMM d, y').format(prayerTimes.date),
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.white.withValues(alpha: 0.85),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  _getHijriDate(prayerTimes.date),
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white.withValues(alpha: 0.75),
                    fontWeight: FontWeight.w400,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNextPrayerCard(PrayerInfo nextPrayer) {
    final timeUntil = nextPrayer.time.difference(DateTime.now());
    final hours = timeUntil.inHours;
    final minutes = timeUntil.inMinutes % 60;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.secondaryGold.withValues(alpha: 0.1),
            AppColors.secondaryGold.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(
          color: AppColors.secondaryGold.withValues(alpha: 0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.secondaryGold.withValues(alpha: 0.15),
            blurRadius: 15,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: AppColors.secondaryGold.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.schedule,
                  color: AppColors.secondaryGold,
                  size: 20.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Text(
                'Next Prayer',
                style: AppTextStyles.titleMedium.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    nextPrayer.name,
                    style: AppTextStyles.headlineSmall.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    DateFormat('h:mm a').format(nextPrayer.time),
                    style: AppTextStyles.titleLarge.copyWith(
                      color: AppColors.secondaryGold,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'Time Remaining',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    hours > 0 ? '${hours}h ${minutes}m' : '${minutes}m',
                    style: AppTextStyles.titleMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentPrayerCard(PrayerInfo currentPrayer) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              Icons.access_time,
              color: Theme.of(context).colorScheme.primary,
              size: 24.sp,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Current Prayer Time',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  currentPrayer.name,
                  style: AppTextStyles.titleLarge.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          Text(
            DateFormat('h:mm a').format(currentPrayer.time),
            style: AppTextStyles.titleMedium.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrayerTimesList(PrayerTimesModel prayerTimes) {
    final prayers = [
      {'name': 'Fajr', 'time': prayerTimes.fajr, 'icon': Icons.wb_twilight},
      {'name': 'Sunrise', 'time': prayerTimes.sunrise, 'icon': Icons.wb_sunny},
      {
        'name': 'Dhuhr',
        'time': prayerTimes.dhuhr,
        'icon': Icons.wb_sunny_outlined,
      },
      {'name': 'Asr', 'time': prayerTimes.asr, 'icon': Icons.wb_cloudy},
      {
        'name': 'Maghrib',
        'time': prayerTimes.maghrib,
        'icon': Icons.wb_twilight,
      },
      {'name': 'Isha', 'time': prayerTimes.isha, 'icon': Icons.nights_stay},
    ];

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 15,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with toggle button
          Padding(
            padding: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 8.w),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Today\'s Prayer Times',
                    style: AppTextStyles.titleLarge.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w700,
                      letterSpacing: 0.3,
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                // Arabic/Transliteration toggle - fixed size
                Container(
                  width: 44.w,
                  height: 32.h,
                  decoration: BoxDecoration(
                    color: AppColors.primaryGreen.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10.r),
                    border: Border.all(
                      color: AppColors.primaryGreen.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(10.r),
                      onTap: () {
                        setState(() {
                          _showArabic = !_showArabic;
                        });
                      },
                      child: Center(
                        child: Text(
                          _showArabic ? 'ع' : 'A',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.primaryGreen,
                            fontWeight: FontWeight.w700,
                            fontSize: 16.sp,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          ...prayers.asMap().entries.map((entry) {
            final index = entry.key;
            final prayer = entry.value;
            final isLast = index == prayers.length - 1;
            final prayerName = prayer['name'] as String;
            final isCurrentPrayer = _isCurrentPrayer(prayerName, prayerTimes);
            final dua = DuaService.getDuaForPrayer(prayerName);

            return Column(
              children: [
                // Clean, spacious prayer card with room for full duas
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
                  padding: EdgeInsets.all(20.w),
                  decoration: BoxDecoration(
                    color:
                        isCurrentPrayer
                            ? _getPrayerColor(
                              prayerName,
                            ).withValues(alpha: 0.05)
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(16.r),
                    border:
                        isCurrentPrayer
                            ? Border.all(
                              color: _getPrayerColor(
                                prayerName,
                              ).withValues(alpha: 0.3),
                              width: 2,
                            )
                            : null,
                    // Subtle glow for current prayer
                    boxShadow:
                        isCurrentPrayer
                            ? [
                              BoxShadow(
                                color: _getPrayerColor(
                                  prayerName,
                                ).withValues(alpha: 0.2),
                                blurRadius: 12,
                                spreadRadius: 0,
                                offset: const Offset(0, 4),
                              ),
                            ]
                            : null,
                  ),
                  child: Row(
                    children: [
                      // Prayer icon
                      Container(
                        width: 48.w,
                        height: 48.w,
                        decoration: BoxDecoration(
                          color: _getPrayerColor(
                            prayerName,
                          ).withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Icon(
                          prayer['icon'] as IconData,
                          color: _getPrayerColor(prayerName),
                          size: 24.sp,
                        ),
                      ),

                      SizedBox(width: 16.w),

                      // Prayer name and dua
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Prayer name with NOW badge
                            Row(
                              children: [
                                Text(
                                  prayerName,
                                  style: AppTextStyles.titleMedium.copyWith(
                                    color:
                                        Theme.of(context).colorScheme.onSurface,
                                    fontWeight:
                                        isCurrentPrayer
                                            ? FontWeight.w700
                                            : FontWeight.w600,
                                    fontSize: 18.sp,
                                  ),
                                ),
                                if (isCurrentPrayer) ...[
                                  SizedBox(width: 8.w),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 8.w,
                                      vertical: 3.h,
                                    ),
                                    decoration: BoxDecoration(
                                      color: _getPrayerColor(prayerName),
                                      borderRadius: BorderRadius.circular(6.r),
                                    ),
                                    child: Text(
                                      'NOW',
                                      style: AppTextStyles.bodySmall.copyWith(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w700,
                                        fontSize: 10.sp,
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),

                            // Dua with toggle - shows full text
                            if (dua != null) ...[
                              SizedBox(height: 6.h),
                              GestureDetector(
                                onTap: () {
                                  setState(() {
                                    _showArabic = !_showArabic;
                                  });
                                },
                                child: Text(
                                  _showArabic
                                      ? dua['arabic']!
                                      : dua['transliteration']!,
                                  style: AppTextStyles.bodySmall.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface
                                        .withValues(alpha: 0.65),
                                    fontStyle:
                                        _showArabic
                                            ? FontStyle.normal
                                            : FontStyle.italic,
                                    fontSize: _showArabic ? 14.sp : 11.sp,
                                    height: 1.4,
                                  ),
                                  // Removed maxLines and overflow to show full dua
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),

                      SizedBox(width: 12.w),

                      // Prayer time
                      Text(
                        DateFormat('h:mm a').format(prayer['time'] as DateTime),
                        style: AppTextStyles.titleMedium.copyWith(
                          color: _getPrayerColor(prayerName),
                          fontWeight: FontWeight.w700,
                          fontSize: 16.sp,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ],
                  ),
                ),
                if (!isLast)
                  Divider(
                    height: 1,
                    color: Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.2),
                    indent: 20.w,
                    endIndent: 20.w,
                  ),
              ],
            );
          }),
          SizedBox(height: 8.h),
        ],
      ),
    );
  }

  // Get Hijri date string
  String _getHijriDate(DateTime gregorianDate) {
    try {
      final hijriDate = HijriCalendar.fromDate(gregorianDate);
      return '${hijriDate.hDay} ${hijriDate.longMonthName} ${hijriDate.hYear} AH';
    } catch (e) {
      return 'Hijri date unavailable';
    }
  }

  // Check if prayer is currently active
  bool _isCurrentPrayer(String prayerName, PrayerTimesModel prayerTimes) {
    final now = DateTime.now();

    switch (prayerName.toLowerCase()) {
      case 'fajr':
        return now.isAfter(prayerTimes.fajr) &&
            now.isBefore(prayerTimes.sunrise);
      case 'sunrise':
        return now.isAfter(prayerTimes.sunrise) &&
            now.isBefore(prayerTimes.dhuhr);
      case 'dhuhr':
        return now.isAfter(prayerTimes.dhuhr) && now.isBefore(prayerTimes.asr);
      case 'asr':
        return now.isAfter(prayerTimes.asr) &&
            now.isBefore(prayerTimes.maghrib);
      case 'maghrib':
        return now.isAfter(prayerTimes.maghrib) &&
            now.isBefore(prayerTimes.isha);
      case 'isha':
        // Isha is active until next Fajr (next day)
        final nextFajr = prayerTimes.fajr.add(const Duration(days: 1));
        return now.isAfter(prayerTimes.isha) && now.isBefore(nextFajr);
      default:
        return false;
    }
  }

  Color _getPrayerColor(String prayerName) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    switch (prayerName.toLowerCase()) {
      case 'fajr':
        return isDark
            ? const Color(0xFF64B5F6)
            : const Color(0xFF4A90E2); // Lighter blue for dark mode
      case 'sunrise':
        return isDark
            ? const Color(0xFFFFB74D)
            : const Color(0xFFFF9500); // Lighter orange for dark mode
      case 'dhuhr':
        return isDark
            ? const Color(0xFFFFF176)
            : const Color(0xFFFFD700); // Lighter gold for dark mode
      case 'asr':
        return isDark
            ? const Color(0xFFFF8A65)
            : const Color(0xFFFF6B35); // Lighter orange-red for dark mode
      case 'maghrib':
        return isDark
            ? const Color(0xFFBA68C8)
            : const Color(0xFF8E44AD); // Lighter purple for dark mode
      case 'isha':
        return isDark
            ? const Color(0xFF81C784)
            : const Color(
              0xFF2E7D32,
            ); // Green instead of dark blue for better visibility
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }
}
