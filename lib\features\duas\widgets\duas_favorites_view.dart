import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/dua_models.dart';
import '../cubit/duas_cubit.dart';

class DuasFavoritesView extends StatelessWidget {
  final List<DuaModel> favoriteDuas;

  const DuasFavoritesView({
    super.key,
    required this.favoriteDuas,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
          child: Row(
            children: [
              Icon(
                Icons.favorite,
                color: Colors.red,
                size: 28.sp,
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Favorite Duas',
                      style: AppTextStyles.headlineSmall.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${favoriteDuas.length} duas saved',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: 16.h),

        // Favorites List
        if (favoriteDuas.isEmpty)
          _buildEmptyState(context)
        else
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              itemCount: favoriteDuas.length,
              itemBuilder: (context, index) {
                final dua = favoriteDuas[index];
                return _buildFavoriteCard(context, dua);
              },
            ),
          ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.favorite_border,
              size: 64.sp,
              color: Colors.grey,
            ),
            SizedBox(height: 16.h),
            Text(
              'No favorite duas yet',
              style: AppTextStyles.headlineSmall.copyWith(
                color: Colors.grey,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Tap the heart icon on any dua to add it to your favorites',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            ElevatedButton(
              onPressed: () => context.read<DuasCubit>().backToCategories(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGreen,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Text(
                'Browse Duas',
                style: AppTextStyles.labelLarge.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoriteCard(BuildContext context, DuaModel dua) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => context.read<DuasCubit>().viewDua(dua),
        borderRadius: BorderRadius.circular(16.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and favorite
              Row(
                children: [
                  Expanded(
                    child: Text(
                      dua.title,
                      style: AppTextStyles.labelLarge.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  IconButton(
                    onPressed: () => context.read<DuasCubit>().toggleFavorite(dua),
                    icon: Icon(
                      Icons.favorite,
                      color: Colors.red,
                      size: 24.sp,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 8.h),

              // Arabic text preview
              Text(
                dua.arabicText.length > 100 
                    ? '${dua.arabicText.substring(0, 100)}...'
                    : dua.arabicText,
                style: AppTextStyles.bodyLarge.copyWith(
                  fontFamily: 'Arabic',
                  height: 1.8,
                  color: AppColors.primaryGreen,
                ),
                textAlign: TextAlign.right,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              SizedBox(height: 8.h),

              // English translation preview
              Text(
                dua.englishTranslation.length > 120
                    ? '${dua.englishTranslation.substring(0, 120)}...'
                    : dua.englishTranslation,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[600],
                  height: 1.4,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              SizedBox(height: 12.h),

              // Benefits preview
              if (dua.benefits.isNotEmpty) ...[
                Row(
                  children: [
                    Icon(
                      Icons.star,
                      color: Colors.amber,
                      size: 16.sp,
                    ),
                    SizedBox(width: 4.w),
                    Expanded(
                      child: Text(
                        dua.benefits.first,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.amber[700],
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],

              SizedBox(height: 8.h),

              // Quick action button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => context.read<DuasCubit>().markDuaRecited(dua),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryGreen.withValues(alpha: 0.1),
                    foregroundColor: AppColors.primaryGreen,
                    elevation: 0,
                    padding: EdgeInsets.symmetric(vertical: 8.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                  child: Text(
                    'Mark as Recited',
                    style: AppTextStyles.labelMedium.copyWith(
                      color: AppColors.primaryGreen,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
