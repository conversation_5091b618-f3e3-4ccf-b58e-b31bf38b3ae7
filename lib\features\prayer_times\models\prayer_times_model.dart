import 'package:equatable/equatable.dart';

class PrayerTimesModel extends Equatable {
  final DateTime fajr;
  final DateTime sunrise;
  final DateTime dhuhr;
  final DateTime asr;
  final DateTime maghrib;
  final DateTime isha;
  final DateTime date;
  final String locationName;
  final double latitude;
  final double longitude;
  final String calculationMethod;
  final String? nextPrayer;
  final String? timeUntilNextPrayer;

  const PrayerTimesModel({
    required this.fajr,
    required this.sunrise,
    required this.dhuhr,
    required this.asr,
    required this.maghrib,
    required this.isha,
    required this.date,
    required this.locationName,
    required this.latitude,
    required this.longitude,
    required this.calculationMethod,
    this.nextPrayer,
    this.timeUntilNextPrayer,
  });

  @override
  List<Object?> get props => [
    fajr,
    sunrise,
    dhuhr,
    asr,
    maghrib,
    isha,
    date,
    locationName,
    latitude,
    longitude,
    calculationMethod,
    nextPrayer,
    timeUntilNextPrayer,
  ];

  PrayerTimesModel copyWith({
    DateTime? fajr,
    DateTime? sunrise,
    DateTime? dhuhr,
    DateTime? asr,
    DateTime? maghrib,
    DateTime? isha,
    DateTime? date,
    String? locationName,
    double? latitude,
    double? longitude,
    String? calculationMethod,
  }) {
    return PrayerTimesModel(
      fajr: fajr ?? this.fajr,
      sunrise: sunrise ?? this.sunrise,
      dhuhr: dhuhr ?? this.dhuhr,
      asr: asr ?? this.asr,
      maghrib: maghrib ?? this.maghrib,
      isha: isha ?? this.isha,
      date: date ?? this.date,
      locationName: locationName ?? this.locationName,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      calculationMethod: calculationMethod ?? this.calculationMethod,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fajr': fajr.toIso8601String(),
      'sunrise': sunrise.toIso8601String(),
      'dhuhr': dhuhr.toIso8601String(),
      'asr': asr.toIso8601String(),
      'maghrib': maghrib.toIso8601String(),
      'isha': isha.toIso8601String(),
      'date': date.toIso8601String(),
      'locationName': locationName,
      'latitude': latitude,
      'longitude': longitude,
      'calculationMethod': calculationMethod,
    };
  }

  factory PrayerTimesModel.fromJson(Map<String, dynamic> json) {
    return PrayerTimesModel(
      fajr: DateTime.parse(json['fajr']),
      sunrise: DateTime.parse(json['sunrise']),
      dhuhr: DateTime.parse(json['dhuhr']),
      asr: DateTime.parse(json['asr']),
      maghrib: DateTime.parse(json['maghrib']),
      isha: DateTime.parse(json['isha']),
      date: DateTime.parse(json['date']),
      locationName: json['locationName'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      calculationMethod: json['calculationMethod'],
    );
  }

  // Get next prayer time
  PrayerInfo getNextPrayer() {
    final now = DateTime.now();
    final prayers = [
      PrayerInfo('Fajr', fajr),
      PrayerInfo('Sunrise', sunrise),
      PrayerInfo('Dhuhr', dhuhr),
      PrayerInfo('Asr', asr),
      PrayerInfo('Maghrib', maghrib),
      PrayerInfo('Isha', isha),
    ];

    for (final prayer in prayers) {
      if (prayer.time.isAfter(now)) {
        return prayer;
      }
    }

    // If no prayer today, return Fajr of next day
    return PrayerInfo('Fajr', fajr.add(const Duration(days: 1)));
  }

  // Get current prayer time
  PrayerInfo getCurrentPrayer() {
    final now = DateTime.now();

    if (now.isBefore(fajr)) {
      return PrayerInfo('Isha', isha.subtract(const Duration(days: 1)));
    }
    if (now.isBefore(sunrise)) {
      return PrayerInfo('Fajr', fajr);
    }
    if (now.isBefore(dhuhr)) {
      return PrayerInfo('Sunrise', sunrise);
    }
    if (now.isBefore(asr)) {
      return PrayerInfo('Dhuhr', dhuhr);
    }
    if (now.isBefore(maghrib)) {
      return PrayerInfo('Asr', asr);
    }
    if (now.isBefore(isha)) {
      return PrayerInfo('Maghrib', maghrib);
    }

    return PrayerInfo('Isha', isha);
  }
}

class PrayerInfo extends Equatable {
  final String name;
  final DateTime time;

  const PrayerInfo(this.name, this.time);

  @override
  List<Object?> get props => [name, time];
}

class LocationModel extends Equatable {
  final double latitude;
  final double longitude;
  final String city;
  final String state;
  final String country;
  final String fullAddress;

  const LocationModel({
    required this.latitude,
    required this.longitude,
    required this.city,
    required this.state,
    required this.country,
    required this.fullAddress,
  });

  @override
  List<Object?> get props => [
    latitude,
    longitude,
    city,
    state,
    country,
    fullAddress,
  ];

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'city': city,
      'state': state,
      'country': country,
      'fullAddress': fullAddress,
    };
  }

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      latitude: json['latitude'],
      longitude: json['longitude'],
      city: json['city'],
      state: json['state'],
      country: json['country'],
      fullAddress: json['fullAddress'],
    );
  }
}
