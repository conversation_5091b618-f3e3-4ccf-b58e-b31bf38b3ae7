import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../presentation/widgets/gradient_background.dart';
import '../cubit/quran_cubit.dart';
import '../cubit/quran_state.dart';
import '../widgets/quran_loading_widget.dart';
import '../widgets/surahs_list_view.dart';
import '../widgets/quran_continuous_reading_view.dart';
import '../widgets/quran_search_view.dart';
import '../widgets/quran_settings_view.dart';
import '../widgets/quran_bookmarks_view.dart';

class QuranScreen extends StatefulWidget {
  final bool quickInit;

  const QuranScreen({super.key, this.quickInit = false});

  @override
  State<QuranScreen> createState() => _QuranScreenState();
}

class _QuranScreenState extends State<QuranScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    // Initialize Quran (quick init for Islamic features, full init otherwise)
    if (widget.quickInit) {
      context.read<QuranCubit>().initializeQuranQuick();
    } else {
      context.read<QuranCubit>().initializeQuran();
    }
    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<QuranCubit, QuranState>(
      builder: (context, state) {
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) async {
            if (didPop) return;

            // Handle different states
            if (state is QuranSurahReading ||
                state is QuranSearchResults ||
                state is QuranSettings) {
              // Go back to Surahs list
              context.read<QuranCubit>().backToSurahs();
            } else {
              // Show exit confirmation for main Quran screen
              final navigator = Navigator.of(context);
              final shouldExit = await _showExitConfirmation(context);
              if (shouldExit && mounted) {
                navigator.pop();
              }
            }
          },
          child: Scaffold(
            body: GradientBackground(
              child: SafeArea(
                child: Column(
                  children: [
                    // Header (hide for bookmarks screen)
                    BlocBuilder<QuranCubit, QuranState>(
                      builder: (context, state) {
                        if (state is QuranBookmarks) {
                          return const SizedBox.shrink(); // No header for bookmarks
                        }
                        return _buildHeader();
                      },
                    ),

                    // Content
                    Expanded(
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: BlocBuilder<QuranCubit, QuranState>(
                          builder: (context, state) {
                            if (state is QuranLoading) {
                              return QuranLoadingWidget(
                                progress: state.progress,
                                message: state.message,
                              );
                            } else if (state is QuranNetworkError) {
                              return _buildNetworkErrorWidget(state.message);
                            } else if (state is QuranError) {
                              return _buildErrorWidget(state.message);
                            } else if (state is QuranSurahsLoaded) {
                              return SurahsListView(
                                surahs: state.surahs,
                                preferences: state.preferences,
                                onSurahTap:
                                    (surah) => context
                                        .read<QuranCubit>()
                                        .openSurah(surah.number),
                                onSearch:
                                    (query) => context
                                        .read<QuranCubit>()
                                        .searchQuran(query),
                              );
                            } else if (state is QuranSurahReading) {
                              return QuranContinuousReadingView(
                                surah: state.surah,
                                preferences: state.preferences,
                                onBackToSurahs:
                                    () =>
                                        context
                                            .read<QuranCubit>()
                                            .backToSurahs(),
                                onPreferencesChanged:
                                    (prefs) => context
                                        .read<QuranCubit>()
                                        .updatePreferences(prefs),
                              );
                            } else if (state is QuranSearchResults) {
                              return QuranSearchView(
                                results: state.results,
                                query: state.query,
                                preferences: state.preferences,
                              );
                            } else if (state is QuranBookmarks) {
                              return const QuranBookmarksView();
                            } else if (state is QuranSettings) {
                              return QuranSettingsView(
                                preferences: state.preferences,
                                availableTranslations:
                                    state.availableTranslations,
                                onPreferencesChanged:
                                    (prefs) => context
                                        .read<QuranCubit>()
                                        .updatePreferences(prefs),
                              );
                            }
                            return const QuranLoadingWidget();
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      child: BlocBuilder<QuranCubit, QuranState>(
        builder: (context, state) {
          return Row(
            children: [
              // Back button
              Container(
                decoration: BoxDecoration(
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.2)
                          : Colors.black.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: IconButton(
                  onPressed: () async {
                    if (state is QuranSurahReading ||
                        state is QuranSearchResults ||
                        state is QuranSettings) {
                      // Go back to Surahs list
                      context.read<QuranCubit>().backToSurahs();
                    } else {
                      // Exit confirmation
                      final navigator = Navigator.of(context);
                      final shouldExit = await _showExitConfirmation(context);
                      if (shouldExit && mounted) {
                        navigator.pop();
                      }
                    }
                  },
                  icon: Icon(
                    Icons.arrow_back_ios_new,
                    color:
                        Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black87,
                    size: 20.sp,
                  ),
                ),
              ),

              SizedBox(width: 16.w),

              // Title
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getScreenTitle(state),
                      style: AppTextStyles.headlineMedium.copyWith(
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.white
                                : Colors.black87,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    Text(
                      _getScreenSubtitle(state),
                      style: AppTextStyles.bodyMedium.copyWith(
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withValues(alpha: 0.8)
                                : Colors.black.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),

              // Action buttons
              if (state is QuranSurahsLoaded) ...[
                // Bookmarks button
                Container(
                  decoration: BoxDecoration(
                    color:
                        Theme.of(context).brightness == Brightness.dark
                            ? Colors.white.withValues(alpha: 0.2)
                            : Colors.black.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: IconButton(
                    onPressed: () => context.read<QuranCubit>().showBookmarks(),
                    icon: Icon(
                      Icons.bookmark,
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black87,
                      size: 20.sp,
                    ),
                  ),
                ),

                SizedBox(width: 12.w),

                // Settings button
                Container(
                  decoration: BoxDecoration(
                    color:
                        Theme.of(context).brightness == Brightness.dark
                            ? Colors.white.withValues(alpha: 0.2)
                            : Colors.black.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: IconButton(
                    onPressed: () => context.read<QuranCubit>().showSettings(),
                    icon: Icon(
                      Icons.settings,
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black87,
                      size: 20.sp,
                    ),
                  ),
                ),
              ],
            ],
          );
        },
      ),
    );
  }

  String _getScreenTitle(QuranState state) {
    if (state is QuranSurahReading) {
      return state.surah.englishName;
    } else if (state is QuranSearchResults) {
      return 'Search Results';
    } else if (state is QuranSettings) {
      return 'Settings';
    }
    return 'Holy Quran';
  }

  String _getScreenSubtitle(QuranState state) {
    if (state is QuranSurahReading) {
      return '${state.surah.englishNameTranslation} • ${state.surah.numberOfAyahs} Ayahs';
    } else if (state is QuranSearchResults) {
      return '${state.results.length} results found';
    } else if (state is QuranSettings) {
      return 'Reading preferences';
    } else if (state is QuranSurahsLoaded) {
      return '${state.surahs.length} chapters';
    }
    return 'Read and reflect upon the divine guidance';
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64.sp,
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.7)
                      : Colors.black.withValues(alpha: 0.7),
            ),

            SizedBox(height: 16.h),

            Text(
              'Unable to Load Quran',
              style: AppTextStyles.titleLarge.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 8.h),

            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.8)
                        : Colors.black.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 24.h),

            ElevatedButton(
              onPressed: () => context.read<QuranCubit>().refreshQuran(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGreen,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Text(
                'Try Again',
                style: AppTextStyles.titleMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.wifi_off,
              size: 64.sp,
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.7)
                      : Colors.black.withValues(alpha: 0.7),
            ),

            SizedBox(height: 16.h),

            Text(
              'Connection Required',
              style: AppTextStyles.titleLarge.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 8.h),

            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.8)
                        : Colors.black.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 24.h),

            ElevatedButton(
              onPressed: () => context.read<QuranCubit>().refreshQuran(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGreen,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Text(
                'Retry Connection',
                style: AppTextStyles.titleMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Show exit confirmation dialog
  Future<bool> _showExitConfirmation(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext dialogContext) {
            return AlertDialog(
              backgroundColor:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[900]
                      : Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16.r),
              ),
              title: Row(
                children: [
                  Icon(
                    Icons.menu_book,
                    color: AppColors.primaryGreen,
                    size: 24.sp,
                  ),
                  SizedBox(width: 12.w),
                  Text(
                    'Exit Quran?',
                    style: AppTextStyles.titleMedium.copyWith(
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black87,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              content: Text(
                'Are you sure you want to exit? Your reading progress will be saved.',
                style: AppTextStyles.bodyMedium.copyWith(
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.8)
                          : Colors.black.withValues(alpha: 0.7),
                ),
              ),
              actions: [
                // Cancel button
                TextButton(
                  onPressed: () => Navigator.of(dialogContext).pop(false),
                  child: Text(
                    'Cancel',
                    style: AppTextStyles.titleSmall.copyWith(
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.8)
                              : Colors.black.withValues(alpha: 0.7),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),

                // Exit button
                ElevatedButton(
                  onPressed: () => Navigator.of(dialogContext).pop(true),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryGreen,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(
                      horizontal: 20.w,
                      vertical: 8.h,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                  child: Text(
                    'Exit',
                    style: AppTextStyles.titleSmall.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            );
          },
        ) ??
        false;
  }
}
