import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:sensors_plus/sensors_plus.dart';
import 'package:flutter_compass/flutter_compass.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../core/services/logger_service.dart';
import 'package:geocoding/geocoding.dart';
import '../models/qibla_model.dart';
import '../services/qibla_calculation_service.dart';
import 'qibla_state.dart';

// Custom exceptions for better error handling
class LocationServiceException implements Exception {
  final String message;
  const LocationServiceException(this.message);
  @override
  String toString() => message;
}

class QiblaCubit extends Cubit<QiblaState> {
  QiblaCubit() : super(QiblaInitial());

  StreamSubscription<CompassEvent>? _compassSubscription;
  StreamSubscription<AccelerometerEvent>? _accelerometerSubscription;
  Timer? _updateTimer;

  Position? _currentPosition;
  String? _locationName;
  double _qiblaDirection = 0.0;
  double _magneticDeclination = 0.0;
  double _distanceToKaaba = 0.0;

  // Compass smoothing
  final List<double> _headingHistory = [];
  static const int _historySize = 5;

  /// Initialize Qibla finder
  Future<void> initializeQibla() async {
    try {
      emit(
        const QiblaLoading(
          progress: 0.0,
          message: 'Starting initialization...',
        ),
      );
      LoggerService.qibla('🧭 QIBLA: Initializing Qibla finder...');

      // Step 1: Check and request permissions (0-30%)
      emit(
        const QiblaLoading(progress: 0.1, message: 'Checking permissions...'),
      );
      final bool hasPermissions = await _checkPermissions();
      if (!hasPermissions) {
        emit(
          QiblaError(
            'Location and sensor permissions are required for accurate Qibla direction',
          ),
        );
        return;
      }
      emit(const QiblaLoading(progress: 0.3, message: 'Permissions granted'));

      // Step 2: Get current location (30-70%)
      emit(
        const QiblaLoading(progress: 0.4, message: 'Getting your location...'),
      );
      await _getCurrentLocation();
      emit(const QiblaLoading(progress: 0.7, message: 'Location found'));

      // Step 3: Calculate Qibla direction (70-85%)
      emit(
        const QiblaLoading(
          progress: 0.75,
          message: 'Calculating Qibla direction...',
        ),
      );
      _calculateQiblaDirection();
      emit(const QiblaLoading(progress: 0.85, message: 'Direction calculated'));

      // Step 4: Start compass monitoring (85-100%)
      emit(
        const QiblaLoading(progress: 0.9, message: 'Initializing compass...'),
      );
      await _startCompassMonitoring();
      emit(const QiblaLoading(progress: 1.0, message: 'Complete'));

      LoggerService.qibla('🧭 QIBLA: Initialization complete');
    } catch (e) {
      LoggerService.error('❌ QIBLA: Initialization error: $e');

      // Provide specific error messages based on exception type
      if (e is LocationServiceException) {
        emit(QiblaLocationServiceError(e.message));
      } else {
        emit(QiblaError('Failed to initialize Qibla finder: ${e.toString()}'));
      }
    }
  }

  /// Check and request necessary permissions
  Future<bool> _checkPermissions() async {
    try {
      LoggerService.qibla('🧭 QIBLA: Checking permissions...');

      // Check location permission
      final locationStatus = await Permission.location.status;
      if (!locationStatus.isGranted) {
        final result = await Permission.location.request();
        if (!result.isGranted) {
          LoggerService.qibla('❌ QIBLA: Location permission denied');
          return false;
        }
      }

      // Check sensor permissions (usually granted by default on most devices)
      LoggerService.qibla('✅ QIBLA: All permissions granted');
      return true;
    } catch (e) {
      LoggerService.error('❌ QIBLA: Permission check error: $e');
      return false;
    }
  }

  /// Get current location
  Future<void> _getCurrentLocation() async {
    try {
      LoggerService.qibla('🧭 QIBLA: Getting current location...');

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw LocationServiceException(
          'Location services are disabled. Please turn on GPS in your device settings to find Qibla direction.',
        );
      }

      // Check permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw LocationServiceException(
            'Location permission is required to find Qibla direction. Please allow location access.',
          );
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw LocationServiceException(
          'Location permissions are permanently denied. Please enable location access in your device settings.',
        );
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 30),
        ),
      );
      _currentPosition = position;

      // Get location name using geocoding
      try {
        final placemarks = await placemarkFromCoordinates(
          position.latitude,
          position.longitude,
        );

        if (placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          final city =
              placemark.locality ??
              placemark.subAdministrativeArea ??
              'Unknown';
          final state = placemark.administrativeArea ?? 'Unknown';
          final country = placemark.country ?? 'Unknown';
          _locationName = '$city, $state, $country';
        } else {
          _locationName = 'Unknown Location';
        }
      } catch (e) {
        LoggerService.qibla('⚠️ QIBLA: Could not get location name: $e');
        _locationName = 'Unknown Location';
      }

      LoggerService.qibla(
        '🧭 QIBLA: Location: $_locationName (${position.latitude}, ${position.longitude})',
      );
    } catch (e) {
      LoggerService.error('❌ QIBLA: Location error: $e');

      // Convert location errors to user-friendly messages
      if (e is LocationServiceException) {
        rethrow; // Already user-friendly
      } else if (e.toString().contains('location') ||
          e.toString().contains('GPS')) {
        throw LocationServiceException(
          'Unable to get your location. Please ensure GPS is enabled and try again.',
        );
      } else {
        throw Exception('Unable to get current location: ${e.toString()}');
      }
    }
  }

  /// Calculate Qibla direction and related data
  void _calculateQiblaDirection() {
    if (_currentPosition == null) return;

    try {
      LoggerService.qibla('🧭 QIBLA: Calculating Qibla direction...');

      // Calculate Qibla direction
      _qiblaDirection = QiblaCalculationService.calculateQiblaDirection(
        userLatitude: _currentPosition!.latitude,
        userLongitude: _currentPosition!.longitude,
      );

      // Calculate distance to Kaaba
      _distanceToKaaba = QiblaCalculationService.calculateDistanceToKaaba(
        userLatitude: _currentPosition!.latitude,
        userLongitude: _currentPosition!.longitude,
      );

      // Get magnetic declination
      _magneticDeclination = QiblaCalculationService.getMagneticDeclination(
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
      );

      LoggerService.qibla(
        '🧭 QIBLA: Qibla direction: ${_qiblaDirection.toStringAsFixed(2)}°',
      );
      LoggerService.qibla(
        '🧭 QIBLA: Distance: ${_distanceToKaaba.toStringAsFixed(2)} km',
      );
    } catch (e) {
      LoggerService.error('❌ QIBLA: Calculation error: $e');
    }
  }

  /// Start compass monitoring
  Future<void> _startCompassMonitoring() async {
    try {
      LoggerService.qibla('🧭 QIBLA: Starting compass monitoring...');

      // Listen to compass events
      _compassSubscription = FlutterCompass.events?.listen((
        CompassEvent event,
      ) {
        if (event.heading != null) {
          _updateHeading(event.heading!);
        }
      });

      // Start update timer for smooth updates
      _updateTimer = Timer.periodic(const Duration(milliseconds: 100), (_) {
        _emitCurrentState();
      });
    } catch (e) {
      LoggerService.error('❌ QIBLA: Compass monitoring error: $e');
      emit(QiblaError('Unable to access device compass: ${e.toString()}'));
    }
  }

  /// Update heading with smoothing
  void _updateHeading(double newHeading) {
    // Add to history for smoothing
    _headingHistory.add(newHeading);
    if (_headingHistory.length > _historySize) {
      _headingHistory.removeAt(0);
    }
  }

  /// Get smoothed heading
  double _getSmoothedHeading() {
    if (_headingHistory.isEmpty) return 0.0;

    // Simple moving average
    double sum = _headingHistory.reduce((a, b) => a + b);
    return sum / _headingHistory.length;
  }

  /// Emit current Qibla state
  void _emitCurrentState() {
    if (_currentPosition == null) return;

    try {
      final double currentHeading = _getSmoothedHeading();

      // Adjust for magnetic declination
      final double trueHeading =
          QiblaCalculationService.adjustForMagneticDeclination(
            magneticBearing: currentHeading,
            declination: _magneticDeclination,
          );

      // Calculate offset from Qibla
      final double offset = QiblaCalculationService.calculateQiblaOffset(
        currentHeading: trueHeading,
        qiblaDirection: _qiblaDirection,
      );

      // Check if facing Qibla
      final bool isFacingQibla = QiblaCalculationService.isFacingQibla(
        currentHeading: trueHeading,
        qiblaDirection: _qiblaDirection,
      );

      // Get direction description
      final String directionDescription =
          QiblaCalculationService.getDirectionDescription(_qiblaDirection);

      final qiblaModel = QiblaModel(
        qiblaDirection: _qiblaDirection,
        currentHeading: trueHeading,
        distanceToKaaba: _distanceToKaaba,
        magneticDeclination: _magneticDeclination,
        locationName: _locationName ?? 'Unknown Location',
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
        isFacingQibla: isFacingQibla,
        offset: offset,
        directionDescription: directionDescription,
        lastUpdated: DateTime.now(),
      );

      emit(QiblaLoaded(qiblaModel));
    } catch (e) {
      LoggerService.error('❌ QIBLA: State emission error: $e');
    }
  }

  /// Refresh location and recalculate
  Future<void> refreshLocation() async {
    try {
      emit(QiblaLoading());
      await _getCurrentLocation();
      _calculateQiblaDirection();
      LoggerService.qibla('🧭 QIBLA: Location refreshed');
    } catch (e) {
      LoggerService.error('❌ QIBLA: Refresh error: $e');
      emit(QiblaError('Failed to refresh location: ${e.toString()}'));
    }
  }

  @override
  Future<void> close() {
    _compassSubscription?.cancel();
    _accelerometerSubscription?.cancel();
    _updateTimer?.cancel();
    return super.close();
  }
}
