import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../presentation/screens/main_screen.dart';
import '../../presentation/screens/islamic_features/islamic_features_screen.dart';
import '../../presentation/screens/ai_chat/ai_chat_screen.dart';
import '../../presentation/screens/social_media/social_media_screen.dart';
import '../../features/prayer_times/screens/prayer_times_screen.dart';
import '../../features/qibla_finder/screens/qibla_finder_screen.dart';
import '../../features/qibla_finder/cubit/qibla_cubit.dart';
import '../../features/mosque_finder/screens/mosque_finder_screen.dart';
import '../../features/mosque_finder/cubit/mosque_finder_cubit.dart';
import '../../features/quran/screens/quran_screen.dart';
import '../../features/duas/screens/duas_screen.dart';
import '../../features/duas/cubit/duas_cubit.dart';
import '../../features/quran/cubit/quran_cubit.dart';
import '../../presentation/screens/settings/settings_screen.dart';
import '../../features/allah_names/presentation/screens/allah_names_screen.dart';

class AppRouter {
  static const String main = '/';
  static const String islamicFeatures = '/islamic-features';
  static const String aiChat = '/ai-chat';
  static const String socialMedia = '/social-media';
  static const String prayerTimes = '/prayer-times';
  static const String qibla = '/qibla';
  static const String mosques = '/mosques';
  static const String quran = '/quran';
  static const String tafsir = '/tafsir';
  static const String hadith = '/hadith';
  static const String duas = '/duas';
  static const String namesOfAllah = '/names-of-allah';
  static const String settings = '/settings';

  static final GoRouter router = GoRouter(
    initialLocation: main,
    routes: [
      GoRoute(path: main, builder: (context, state) => const MainScreen()),
      GoRoute(
        path: islamicFeatures,
        builder: (context, state) => const IslamicFeaturesScreen(),
      ),
      GoRoute(path: aiChat, builder: (context, state) => const AiChatScreen()),
      GoRoute(
        path: socialMedia,
        builder: (context, state) => const SocialMediaScreen(),
      ),
      GoRoute(
        path: prayerTimes,
        builder: (context, state) => const PrayerTimesScreen(),
      ),
      GoRoute(
        path: qibla,
        builder:
            (context, state) => BlocProvider(
              create: (context) => QiblaCubit(),
              child: const QiblaFinderScreen(),
            ),
      ),
      GoRoute(
        path: '/mosque-finder',
        builder:
            (context, state) => BlocProvider(
              create: (context) => MosqueFinderCubit(),
              child: const MosqueFinderScreen(),
            ),
      ),
      GoRoute(
        path: '/quran',
        builder: (context, state) {
          // Check if coming from Islamic features (quick init)
          final quickInit = state.uri.queryParameters['quickInit'] == 'true';
          return BlocProvider(
            create: (context) => QuranCubit(),
            child: QuranScreen(quickInit: quickInit),
          );
        },
      ),
      GoRoute(
        path: settings,
        builder: (context, state) => const SettingsScreen(),
      ),
      GoRoute(
        path: duas,
        builder:
            (context, state) => BlocProvider(
              create: (context) => DuasCubit(),
              child: const DuasScreen(),
            ),
      ),
      GoRoute(
        path: namesOfAllah,
        builder: (context, state) => const AllahNamesScreen(),
      ),
    ],
  );
}
