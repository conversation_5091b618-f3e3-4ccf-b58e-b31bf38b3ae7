import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';
import 'core/router/app_router.dart';
import 'core/di/injection.dart';
import 'presentation/cubit/theme/theme_cubit.dart';
import 'features/prayer_times/cubit/prayer_times_cubit.dart';
import 'features/ai_chat/cubit/ai_chat_cubit.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Load environment variables
  await dotenv.load(fileName: ".env");

  // Initialize Hive
  await Hive.initFlutter();

  // Setup dependency injection
  await setupDependencies();

  // Initialize SharedPreferences
  final prefs = await SharedPreferences.getInstance();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(NoorIslamicApp(prefs: prefs));
}

class NoorIslamicApp extends StatelessWidget {
  final SharedPreferences prefs;

  // Global PageStorage bucket for preserving scroll positions
  static final PageStorageBucket _bucket = PageStorageBucket();

  const NoorIslamicApp({super.key, required this.prefs});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiBlocProvider(
          providers: [
            BlocProvider(create: (context) => ThemeCubit(prefs)),
            BlocProvider(create: (context) => getIt<PrayerTimesCubit>()),
            BlocProvider(create: (context) => getIt<AiChatCubit>()),
          ],
          child: BlocBuilder<ThemeCubit, ThemeState>(
            builder: (context, state) {
              return PageStorage(
                bucket: _bucket,
                child: MaterialApp.router(
                  title: AppConstants.appName,
                  debugShowCheckedModeBanner: false,
                  theme: AppTheme.lightTheme,
                  darkTheme: AppTheme.darkTheme,
                  themeMode: state.themeMode,
                  routerConfig: AppRouter.router,
                ),
              );
            },
          ),
        );
      },
    );
  }
}
