import 'package:equatable/equatable.dart';
import '../models/dua_models.dart';

/// Base state for Duas feature
abstract class DuasState extends Equatable {
  const DuasState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class DuasInitial extends DuasState {
  const DuasInitial();
}

/// Loading state
class DuasLoading extends DuasState {
  final double progress;
  final String message;

  const DuasLoading({this.progress = 0.0, this.message = 'Loading...'});

  @override
  List<Object?> get props => [progress, message];
}

/// Categories loaded state
class DuasCategoriesLoaded extends DuasState {
  final List<DuaCategoryModel> categories;
  final List<DuaModel> favoriteDuas;
  final List<DuaModel> recentDuas;

  const DuasCategoriesLoaded({
    required this.categories,
    required this.favoriteDuas,
    required this.recentDuas,
  });

  @override
  List<Object?> get props => [categories, favoriteDuas, recentDuas];

  DuasCategoriesLoaded copyWith({
    List<DuaCategoryModel>? categories,
    List<DuaModel>? favoriteDuas,
    List<DuaModel>? recentDuas,
  }) {
    return DuasCategoriesLoaded(
      categories: categories ?? this.categories,
      favoriteDuas: favoriteDuas ?? this.favoriteDuas,
      recentDuas: recentDuas ?? this.recentDuas,
    );
  }
}

/// Category duas loaded state
class DuasCategoryDuasLoaded extends DuasState {
  final DuaCategoryModel category;
  final List<DuaModel> duas;

  const DuasCategoryDuasLoaded({required this.category, required this.duas});

  @override
  List<Object?> get props => [category, duas];

  DuasCategoryDuasLoaded copyWith({
    DuaCategoryModel? category,
    List<DuaModel>? duas,
  }) {
    return DuasCategoryDuasLoaded(
      category: category ?? this.category,
      duas: duas ?? this.duas,
    );
  }
}

/// Individual dua viewing state
class DuasViewing extends DuasState {
  final DuaModel dua;
  final DuaProgressModel? progress;
  final bool isPlaying;

  const DuasViewing({required this.dua, this.progress, this.isPlaying = false});

  @override
  List<Object?> get props => [dua, progress, isPlaying];

  DuasViewing copyWith({
    DuaModel? dua,
    DuaProgressModel? progress,
    bool? isPlaying,
  }) {
    return DuasViewing(
      dua: dua ?? this.dua,
      progress: progress ?? this.progress,
      isPlaying: isPlaying ?? this.isPlaying,
    );
  }
}

/// Search results state
class DuasSearchResults extends DuasState {
  final String query;
  final List<DuaModel> results;

  const DuasSearchResults({required this.query, required this.results});

  @override
  List<Object?> get props => [query, results];
}

/// Favorites state
class DuasFavorites extends DuasState {
  final List<DuaModel> favoriteDuas;

  const DuasFavorites({required this.favoriteDuas});

  @override
  List<Object?> get props => [favoriteDuas];

  DuasFavorites copyWith({List<DuaModel>? favoriteDuas}) {
    return DuasFavorites(favoriteDuas: favoriteDuas ?? this.favoriteDuas);
  }
}

/// Recent duas state
class DuasRecent extends DuasState {
  final List<DuaModel> recentDuas;

  const DuasRecent({required this.recentDuas});

  @override
  List<Object?> get props => [recentDuas];

  DuasRecent copyWith({List<DuaModel>? recentDuas}) {
    return DuasRecent(recentDuas: recentDuas ?? this.recentDuas);
  }
}

/// Error state
class DuasError extends DuasState {
  final String message;
  final String? details;

  const DuasError(this.message, {this.details});

  @override
  List<Object?> get props => [message, details];
}
