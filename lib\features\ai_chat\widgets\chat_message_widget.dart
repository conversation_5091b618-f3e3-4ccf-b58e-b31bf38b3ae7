import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/chat_message.dart';

class ChatMessageWidget extends StatefulWidget {
  final ChatMessage message;
  final bool isStreaming;
  final String? streamingContent;

  const ChatMessageWidget({
    super.key,
    required this.message,
    this.isStreaming = false,
    this.streamingContent,
  });

  @override
  State<ChatMessageWidget> createState() => _ChatMessageWidgetState();
}

class _ChatMessageWidgetState extends State<ChatMessageWidget>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  late AnimationController _typewriterController;
  late Animation<int> _typewriterAnimation;

  @override
  void initState() {
    super.initState();

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOutBack),
    );

    if (widget.isStreaming && widget.streamingContent != null) {
      _typewriterController = AnimationController(
        duration: Duration(milliseconds: widget.streamingContent!.length * 15),
        vsync: this,
      );

      _typewriterAnimation = IntTween(
        begin: 0,
        end: widget.streamingContent!.length,
      ).animate(
        CurvedAnimation(parent: _typewriterController, curve: Curves.easeOut),
      );

      _typewriterController.forward();
    }

    // Delayed animation for better visual effect
    Future.delayed(Duration(milliseconds: widget.message.isUser ? 0 : 200), () {
      if (mounted) _fadeController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    if (widget.isStreaming) {
      _typewriterController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: Offset(widget.message.isUser ? 1.0 : -1.0, 0.0),
        end: Offset.zero,
      ).animate(
        CurvedAnimation(parent: _fadeController, curve: Curves.easeOutCubic),
      ),
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          margin: EdgeInsets.only(bottom: 16.h),
          child: Column(
            crossAxisAlignment:
                widget.message.isUser
                    ? CrossAxisAlignment.end
                    : CrossAxisAlignment.start,
            children: [
              _buildMessageBubble(),
              SizedBox(height: 4.h),
              _buildMessageInfo(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMessageBubble() {
    final isUser = widget.message.isUser;
    final isSystem = widget.message.isSystem;

    if (isUser) {
      // User messages with bubble
      return Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
          gradient: _getMessageGradient(isUser, isSystem),
          borderRadius: _getMessageBorderRadius(isUser),
          boxShadow: [
            BoxShadow(
              color: _getShadowColor(isUser, isSystem),
              blurRadius: 12,
              offset: const Offset(0, 3),
              spreadRadius: 0,
            ),
          ],
          border: Border.all(
            color: _getBorderColor(isUser, isSystem),
            width: 1,
          ),
        ),
        child: _buildMessageContent(),
      );
    } else {
      // AI messages - transparent, no bubble
      return Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildMessageContent(),
            if (widget.isStreaming) _buildStreamingIndicator(),
          ],
        ),
      );
    }
  }

  Widget _buildMessageContent() {
    String content = widget.message.content;

    if (widget.isStreaming && widget.streamingContent != null) {
      return AnimatedBuilder(
        animation: _typewriterAnimation,
        builder: (context, child) {
          final displayText = widget.streamingContent!.substring(
            0,
            _typewriterAnimation.value,
          );
          return _buildTextContent(displayText);
        },
      );
    }

    return _buildTextContent(content);
  }

  Widget _buildTextContent(String content) {
    final isUser = widget.message.isUser;
    final isSystem = widget.message.isSystem;

    return SelectableText(
      content,
      style:
          isSystem
              ? AppTextStyles.bodyMedium.copyWith(
                color: AppColors.info,
                fontStyle: FontStyle.italic,
              )
              : AppTextStyles.bodyMedium.copyWith(
                color:
                    isUser
                        ? Colors.white
                        : Theme.of(context).colorScheme.onSurface,
                height: 1.5,
                fontWeight: FontWeight.w400,
                fontSize: isUser ? 14.sp : 15.sp,
              ),
      onTap: () {
        // Copy to clipboard on tap
        Clipboard.setData(ClipboardData(text: content));
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Message copied to clipboard'),
            duration: const Duration(seconds: 2),
            backgroundColor: AppColors.primaryGreen,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStreamingIndicator() {
    return Padding(
      padding: EdgeInsets.only(top: 8.h),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16.w,
            height: 16.w,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                Colors.white.withValues(alpha: 0.7),
              ),
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            'Sheikh Noor is typing...',
            style: AppTextStyles.labelSmall.copyWith(
              color: Colors.white.withValues(alpha: 0.7),
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInfo() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      child: Text(
        widget.message.formattedTime,
        style: AppTextStyles.labelSmall.copyWith(
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
        ),
      ),
    );
  }

  LinearGradient _getMessageGradient(bool isUser, bool isSystem) {
    if (isSystem) {
      return LinearGradient(
        colors: [
          AppColors.info.withValues(alpha: 0.1),
          AppColors.info.withValues(alpha: 0.05),
        ],
      );
    } else if (isUser) {
      return LinearGradient(
        colors: [AppColors.secondaryGold, AppColors.secondaryGoldLight],
      );
    } else {
      return LinearGradient(colors: AppColors.primaryGradient);
    }
  }

  BorderRadius _getMessageBorderRadius(bool isUser) {
    if (isUser) {
      return BorderRadius.only(
        topLeft: Radius.circular(24.r),
        topRight: Radius.circular(8.r),
        bottomLeft: Radius.circular(24.r),
        bottomRight: Radius.circular(24.r),
      );
    } else {
      return BorderRadius.only(
        topLeft: Radius.circular(8.r),
        topRight: Radius.circular(24.r),
        bottomLeft: Radius.circular(24.r),
        bottomRight: Radius.circular(24.r),
      );
    }
  }

  Color _getShadowColor(bool isUser, bool isSystem) {
    if (isSystem) {
      return AppColors.info.withValues(alpha: 0.2);
    } else if (isUser) {
      return AppColors.secondaryGold.withValues(alpha: 0.3);
    } else {
      return AppColors.primaryGreen.withValues(alpha: 0.3);
    }
  }

  Color _getBorderColor(bool isUser, bool isSystem) {
    if (isSystem) {
      return AppColors.info.withValues(alpha: 0.3);
    } else if (isUser) {
      return AppColors.secondaryGold.withValues(alpha: 0.4);
    } else {
      return AppColors.primaryGreen.withValues(alpha: 0.4);
    }
  }
}
